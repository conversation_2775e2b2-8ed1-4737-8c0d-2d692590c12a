import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import { Session, User } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      throw error;
    }
    
    if (data.user) {
      try {
        await createUserRecord(data.user);
      } catch (error) {
        console.error('Failed to sync user to public table:', error);
        throw new Error('Login successful but profile sync failed. Some features may not work.');
      }
    }
    
    return data;
  };

  const createUserRecord = async (user: User, attempt = 1): Promise<void> => {
    console.log(`Attempting to create user record for ${user.email} (attempt ${attempt})`);
    
    try {
      const { error } = await supabase
        .from('users')
        .upsert({
          id: user.id,
          email: user.email,
          created_at: new Date().toISOString(),
          password: '' // Add empty string for required password field
        }, {
          onConflict: 'id'
        });

      if (error) {
        console.error('User record creation failed:', {
          error,
          user: { id: user.id, email: user.email }
        });
        
        // Retry up to 3 times with exponential backoff
        if (attempt < 3) {
          const delay = Math.pow(2, attempt) * 1000;
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return createUserRecord(user, attempt + 1);
        }
        
        throw error;
      }

      console.log('Successfully created user record for:', user.email);
    } catch (error) {
      console.error('Final user record creation failure:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      if (error) throw error;
      
      if (data.user) {
        try {
          await createUserRecord(data.user);
        } catch (error) {
          console.error('Failed to sync user to public table:', error);
          alert('Account created but profile setup failed. Please contact support.');
        }
      }
    } catch (error: any) {
      alert(error.error_description || error.message);
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      alert(error.error_description || error.message);
    }
  };

  return (
    <AuthContext.Provider value={{ user, session, login, logout, signUp }}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
