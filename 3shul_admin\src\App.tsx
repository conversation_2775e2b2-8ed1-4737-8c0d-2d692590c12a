import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import Login from './pages/Login';

// Lazy load other pages
const Dashboard = React.lazy(() => import('./pages/Dashboard.tsx'));
const Cities = React.lazy(() => import('./pages/Cities.tsx'));
const Zones = React.lazy(() => import('./pages/ZonesNew.tsx'));
const Bookings = React.lazy(() => import('./pages/Bookings.tsx'));
const Coupons = React.lazy(() => import('./pages/Coupons.tsx'));

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Login />} />

        {/* Protected routes */}
        <Route
          path="/dashboard"
          element={
            <Layout>
              <React.Suspense fallback={<div>Loading...</div>}>
                <Dashboard />
              </React.Suspense>
            </Layout>
          }
        />
        <Route
          path="/cities"
          element={
            <Layout>
              <React.Suspense fallback={<div>Loading...</div>}>
                <Cities />
              </React.Suspense>
            </Layout>
          }
        />
        <Route
          path="/zones"
          element={
            <Layout>
              <React.Suspense fallback={<div>Loading...</div>}>
                <Zones />
              </React.Suspense>
            </Layout>
          }
        />
        <Route
          path="/bookings"
          element={
            <Layout>
              <React.Suspense fallback={<div>Loading...</div>}>
                <Bookings />
              </React.Suspense>
            </Layout>
          }
        />
        <Route
          path="/coupons"
          element={
            <Layout>
              <React.Suspense fallback={<div>Loading...</div>}>
                <Coupons />
              </React.Suspense>
            </Layout>
          }
        />

        {/* Redirect old screens route to zones page */}
        <Route path="/screens" element={<Navigate to="/zones" replace />} />

      </Routes>
    </Router>
  );
}

export default App;
