# Tech Context

## Technologies Used
- React
- TypeScript
- Supabase (PostgreSQL backend)
- Tailwind CSS
- Vite

## Development Setup
- The project is developed using Visual Studio Code.
- The project uses Node.js and npm for package management.
- The project uses Vite as the build tool.

## Technical Constraints
- The project must be compatible with modern web browsers.
- The project must be responsive and accessible.
- The project must be secure and protect user data.

## Dependencies
- react
- react-dom
- @types/react
- @types/react-dom
- tailwindcss
- postcss
- autoprefixer
- vite

## Database Schema
```sql
-- USERS TABLE
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ZONES TABLE
CREATE TABLE zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  city VARCHAR(100) NOT NULL,
  sub_zone VARCHAR(100) NOT NULL,
  pincode VARCHAR(20),
  description TEXT,
  price_year DECIMAL(10, 2) NOT NULL,
  image_url TEXT,
  isavailable BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BOOKINGS TABLE
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  zone_id UUID REFERENCES zones(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  listing_type VARCHAR(100),
  business_name VARCHAR(255),
  description TEXT,
  phone_number VARCHAR(20),
  video_url TEXT,
  status VARCHAR(20) CHECK (status IN ('Pending', 'Approved', 'Fulfilled', 'Cancelled')) DEFAULT 'Pending',
  payment_id VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BOOKING_ZONES junction table
CREATE TABLE booking_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  zone_id UUID REFERENCES zones(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(booking_id, zone_id)
);

CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  razorpay_payment_id VARCHAR(100) NOT NULL,
  razorpay_order_id VARCHAR(100),
  amount DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'INR',
  method VARCHAR(50),
  status VARCHAR(50),
  email VARCHAR(255),
  contact VARCHAR(20),
  fee DECIMAL(10, 2),
  tax DECIMAL(10, 2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
