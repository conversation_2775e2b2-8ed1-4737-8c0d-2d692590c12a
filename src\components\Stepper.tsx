import { Check } from 'lucide-react';
import { clsx } from 'clsx';

interface Step {
  name: string;
  status: 'complete' | 'current' | 'upcoming';
  clickable: boolean;
}

interface StepperProps {
  steps: Step[];
  onStepClick?: (index: number) => void;
}

export function Stepper({ steps, onStepClick }: StepperProps) {
  return (
    <nav aria-label="Progress">
      <ol role="list" className="flex items-center justify-between">
        {steps.map((step, stepIdx) => (
          <li
            key={step.name}
            className="relative flex flex-col items-center"
          >
            <div className="flex items-center">
              {stepIdx !== 0 && (
                <div 
                  className="absolute right-1/2 top-[15px] h-[2px] w-full bg-gray-200"
                  style={{ width: 'calc(200% - 1rem)', right: '50%', marginLeft: '-50%' }}
                />
              )}
              <button
                type="button"
                onClick={() => step.clickable && onStepClick?.(stepIdx)}
                className={clsx(
                  'relative flex h-8 w-8 items-center justify-center rounded-full z-10',
                  step.clickable ? 'cursor-pointer' : 'cursor-not-allowed opacity-50',
                  step.status === 'complete' ? 'bg-purple-600' : 'bg-white border-2',
                  step.status === 'current' ? 'border-purple-600' : 'border-gray-300'
                )}
                disabled={!step.clickable}
              >
                {step.status === 'complete' ? (
                  <Check className="h-5 w-5 text-white" aria-hidden="true" />
                ) : step.status === 'current' ? (
                  <span className="h-2.5 w-2.5 rounded-full bg-purple-600" />
                ) : (
                  <span className="h-2.5 w-2.5 rounded-full" />
                )}
              </button>
            </div>
            <span
              className={clsx(
                'mt-2 text-xs font-medium text-center',
                step.status === 'complete' ? 'text-purple-600' :
                step.status === 'current' ? 'text-purple-600' :
                'text-gray-500'
              )}
            >
              {step.name}
            </span>
          </li>
        ))}
      </ol>
    </nav>
  );
}





