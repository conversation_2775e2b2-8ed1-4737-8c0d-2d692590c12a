// A simple script to test the webhook function
// Run with: node test-webhook.js YOUR_BOOKING_ID YOUR_SUPABASE_ANON_KEY

const https = require('https');

// Get command line arguments
const bookingId = process.argv[2];
const supabaseKey = process.argv[3];

if (!bookingId || !supabaseKey) {
  console.error('Usage: node test-webhook.js YOUR_BOOKING_ID YOUR_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Construct the URL
const url = `https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook?booking_id=${bookingId}`;

// Set up the request options
const options = {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${supabaseKey}`,
    'Content-Type': 'application/json'
  }
};

console.log(`Testing webhook with booking ID: ${bookingId}`);
console.log(`URL: ${url}`);

// Make the request
const req = https.request(url, options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:');
    try {
      const jsonData = JSON.parse(data);
      console.log(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log(data);
    }
  });
});

req.on('error', (error) => {
  console.error(`Error: ${error.message}`);
});

req.end();
