interface PaymentStatusProps {
  status: 'idle' | 'processing' | 'success' | 'error' | 'failed';
  onRetry?: () => void;
}

export function PaymentStatus({ status, onRetry }: PaymentStatusProps) {
  return (
    <div className="text-center">
      {status === 'idle' && (
        <div className="space-y-4">
          <p className="text-lg font-medium text-gray-900">Ready to process your payment</p>
          <p className="text-sm text-gray-500">
            Please click the button below to proceed with payment.
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-4 rounded-md bg-purple-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              Proceed to Payment
            </button>
          )}
        </div>
      )}

      {status === 'processing' && (
        <div className="space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600 border-t-transparent mx-auto" />
          <p className="text-lg font-medium text-gray-900">Processing your payment...</p>
        </div>
      )}

      {status === 'success' && (
        <div className="space-y-4">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <svg
              className="h-6 w-6 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <p className="text-lg font-medium text-gray-900">Payment successful!</p>
          <p className="text-sm text-gray-500">
            Your booking is now pending approval. We'll send you a confirmation email shortly.
          </p>
        </div>
      )}

      {(status === 'error' || status === 'failed') && (
        <div className="space-y-4">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <p className="text-lg font-medium text-gray-900">
            {status === 'failed' ? 'Payment Failed' : 'Payment Error'}
          </p>
          <p className="text-sm text-gray-500">
            {status === 'failed' 
              ? 'Your payment was not successful. Please try again.' 
              : 'There was an error processing your payment. Please try again.'}
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-4 rounded-md bg-purple-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              Retry Payment
            </button>
          )}
        </div>
      )}
    </div>
  );
}
