# Tech Context

## Core Technologies
- React 18 (TypeScript)
- Vite 5 (Build tool)
- Tailwind CSS 3 (Styling)
- React Router 6 (Routing)
- Headless UI (Component library)
- Supabase (Backend platform)
- Razorpay (Payment gateway)

## Supabase Services
- PostgreSQL database
- Authentication
- (Potentially Storage, Functions in future)

## Development Dependencies
- TypeScript
- ESLint (Code linting)
- PostCSS (CSS processing)
- Date-fns (Date handling)
- Lucide React (Icons)
- @supabase/supabase-js

## Project Structure
- src/
  - components/ (React components)
  - contexts/ (Context providers, e.g., AuthContext)
  - lib/ (Utility/library code, e.g., supabase client)
  - types.ts (Type definitions)
  - utils.ts (Utility functions)

## Build & Run
- `npm run dev` - Start development server
- `npm run build` - Production build
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Configuration Files
- vite.config.ts - Vite configuration
- tailwind.config.js - Tailwind config
- tsconfig*.json - TypeScript configs
- postcss.config.js - PostCSS config
- .env - Environment variables (for Supabase credentials, <PERSON>zorpay keys)
