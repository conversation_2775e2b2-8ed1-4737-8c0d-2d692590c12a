# Razorpay Webhook Integration Guide

This guide will help you properly set up and test the Razorpay webhook integration with your Supabase Edge Function.

## 1. Webhook Configuration in Razorpay Dashboard

1. Log in to your [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Go to **Settings > Webhooks**
3. Click on **Add New Webhook**
4. Enter the following details:
   - **URL:** `https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook`
   - **Secret:** `59KSN@ww3wAKZEc`
   - **Events:** Select at least `payment.authorized` and `payment.captured`
5. Click **Create Webhook**

## 2. Verify Webhook Configuration

After creating the webhook, you should:

1. Click on **Test** next to your webhook in the Razorpay dashboard
2. Select an event type (e.g., `payment.captured`)
3. Click **Send Test Webhook**
4. Check the response status in the Razorpay dashboard

## 3. Understanding the Webhook Flow

The webhook flow works as follows:

1. Customer makes a payment through Razorpay
2. Your frontend code receives a payment ID from Razorpay and updates your booking with this payment ID
3. Razorpay processes the payment and sends a webhook notification to your webhook URL
4. Your webhook function:
   - Verifies the webhook signature
   - Extracts the payment ID and status from the webhook payload
   - Finds the booking associated with that payment ID
   - Updates the booking status to 'success' if the payment was successful

## 4. Common Issues and Solutions

### Issue 1: Webhook Not Reaching Your Function

**Symptoms:**
- No logs in your Supabase function
- Razorpay dashboard shows failed webhook attempts

**Solutions:**
- Verify the webhook URL is correct
- Check if your Supabase function is deployed and accessible
- Test the function directly using the provided test tools

### Issue 2: Payment ID Mismatch

**Symptoms:**
- Webhook function logs show "Could not find booking with this payment ID"

**Solutions:**
- Verify that the payment ID from Razorpay is being correctly saved to your bookings table
- Check the webhook logs to see what payment ID is being received
- Compare with the payment IDs in your bookings table

### Issue 3: Database Permissions

**Symptoms:**
- Webhook function logs show database permission errors

**Solutions:**
- Make sure your SUPABASE_SERVICE_ROLE_KEY has the necessary permissions
- Check if there are any RLS (Row Level Security) policies restricting updates

### Issue 4: Webhook Signature Verification

**Symptoms:**
- Webhook function logs show "Invalid webhook signature"

**Solutions:**
- Verify that the RAZORPAY_WEBHOOK_SECRET environment variable is set correctly
- Make sure the secret matches what's configured in the Razorpay dashboard

## 5. Testing Tools

Use the following HTML files to test and debug your webhook integration:

1. **test-webhook-connection.html**
   - Tests if your webhook endpoint is accessible
   - Verifies basic GET and POST functionality

2. **test-update-booking.html**
   - Directly updates a booking's payment status
   - Simulates a Razorpay webhook call

3. **check-webhook-logs.html**
   - Views webhook logs to see what data is being received
   - Checks bookings and transactions tables

## 6. Manual Testing with Postman

You can also test your webhook with Postman:

1. **URL:** `https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook`
2. **Method:** POST
3. **Headers:**
   - `Content-Type: application/json`
   - `Authorization: Bearer YOUR_SUPABASE_KEY`
   - `X-Razorpay-Signature: YOUR_CALCULATED_SIGNATURE`
4. **Body (raw JSON):**
```json
{
  "event": "payment.captured",
  "payload": {
    "payment": {
      "entity": {
        "id": "YOUR_PAYMENT_ID",
        "status": "captured"
      }
    }
  }
}
```

## 7. Debugging Steps

If you're still having issues:

1. **Check Webhook Logs:**
   - Open check-webhook-logs.html
   - Enter your Supabase key
   - Click "Check Webhook Logs"

2. **Verify Booking Data:**
   - Open check-webhook-logs.html
   - Click "Check Bookings"
   - Verify that bookings have payment_id and payment_status columns

3. **Test Direct Update:**
   - Open test-update-booking.html
   - Enter a booking ID and your Supabase key
   - Click "Update Booking Status"
   - Check if the update succeeds

4. **Test Webhook Endpoint:**
   - Open test-webhook-connection.html
   - Test both GET and POST requests
   - Verify the endpoint is accessible

5. **Check Razorpay Dashboard:**
   - Go to Webhooks section
   - Check the webhook delivery history
   - Look for any failed webhook attempts and error messages

## 8. Example Webhook Payload

Here's an example of what a Razorpay webhook payload looks like:

```json
{
  "entity": "event",
  "account_id": "acc_XXXXXXXXXX",
  "event": "payment.captured",
  "contains": ["payment"],
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_XXXXXXXXXX",
        "entity": "payment",
        "amount": 100000,
        "currency": "INR",
        "status": "captured",
        "order_id": "order_XXXXXXXXXX",
        "method": "card",
        "card_id": "card_XXXXXXXXXX",
        "description": "Zone Booking Payment",
        "email": "<EMAIL>",
        "contact": "+************"
      }
    }
  }
}
```

## 9. Environment Variables

Make sure these environment variables are set in your Supabase Edge Function:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `RAZORPAY_WEBHOOK_SECRET`: Your Razorpay webhook secret (59KSN@ww3wAKZEc)

You can set these in the Supabase dashboard under Edge Functions > razorpay-webhook > Environment Variables.
