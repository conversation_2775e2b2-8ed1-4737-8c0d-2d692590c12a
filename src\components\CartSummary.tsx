import { CartItem, BusinessInfo, PackageType, AppliedCoupon } from '../types';
import { format, differenceInDays, differenceInMonths } from 'date-fns';
import { formatPrice, calculateTotalPrice } from '../utils';
import { Trash2, Package, Monitor } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { RAZORPAY_KEY } from '../lib/config';
import { useState, useEffect } from 'react';
import { CouponInput } from './CouponInput';
import { recordCouponUsage } from '../lib/coupons';

interface CartSummaryProps {
  cartItems: CartItem[];
  onRemoveItem: (zoneId: string) => void;
  onProceedToPayment: () => Promise<string>;
  onPaymentComplete: (bookingId?: string) => void;
  businessInfo?: BusinessInfo;
  userId: string;
}

export function CartSummary({
  cartItems,
  onRemoveItem,
  onProceedToPayment,
  onPaymentComplete,
  businessInfo,
  userId
}: CartSummaryProps) {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasInitiatedPayment, setHasInitiatedPayment] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<AppliedCoupon | null>(null);

  // Get the package type
  const packageType = businessInfo?.packageType;

  // Use the calculateTotalPrice function with package type
  const originalPrice = calculateTotalPrice(cartItems, packageType);
  const discountAmount = appliedCoupon?.discount_amount || 0;
  const totalPrice = originalPrice - discountAmount;

  const resetPaymentState = () => {
    setIsProcessing(false);
    setHasInitiatedPayment(false);
  };

  const handleCouponApplied = (coupon: AppliedCoupon) => {
    setAppliedCoupon(coupon);
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
  };

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const handlePayment = async () => {
    if (isProcessing || hasInitiatedPayment) return; // Prevent multiple clicks

    setIsProcessing(true);
    setHasInitiatedPayment(true);

    try {
      // First create booking with payment_status='pending'
      const bookingId = await onProceedToPayment();
      if (!bookingId) {
        throw new Error('Failed to create booking');
      }

      // Load Razorpay script
      const isScriptLoaded = await loadRazorpayScript();
      if (!isScriptLoaded) {
        throw new Error('Razorpay SDK failed to load');
      }

      const handlePaymentFailure = async (response: any) => {
        try {
          // Update booking with failed status
          const { error: updateError } = await supabase
            .from('bookings')
            .update({
              payment_id: response.razorpay_payment_id || null,
              payment_status: 'failed',
              status: 'Failed',
              updated_at: new Date().toISOString()
            })
            .eq('id', bookingId);

          if (updateError) throw updateError;

          // Show error to user and redirect
          alert('Payment failed. Please try again.');
          navigate('/dashboard'); // Navigate away instead of staying on the page
        } catch (error) {
          console.error('Error handling payment failure:', error);
          alert('An error occurred while processing your payment. Please contact support.');
          navigate('/dashboard'); // Navigate away on error
        }
      };

      const options = {
        key: RAZORPAY_KEY,
        amount: totalPrice * 100, // Convert to paise
        currency: 'INR',
        name: businessInfo?.businessName || 'Your Business Name',
        description: 'Zone Booking Payment',
        image: 'https://your-logo-url.com/logo.png',
        handler: async function (response: any) {
          if (!response.razorpay_payment_id) {
            throw new Error('No payment ID received');
          }

          try {
            // Update booking with success status and coupon information
            const updateData: any = {
              payment_id: response.razorpay_payment_id,
              payment_status: 'success',
              status: 'Pending', // Keep as Pending until admin approval
              email: businessInfo?.email || '',
              updated_at: new Date().toISOString()
            };

            // Add coupon information if applied
            if (appliedCoupon) {
              updateData.coupon_id = appliedCoupon.coupon.id;
              updateData.discount_amount = appliedCoupon.discount_amount;
              updateData.original_amount = originalPrice;
            }

            const { error: updateError } = await supabase
              .from('bookings')
              .update(updateData)
              .eq('id', bookingId);

            if (updateError) throw updateError;

            // Save transaction record
            const { error: transactionError } = await supabase
              .from('transactions')
              .insert([{
                booking_id: bookingId,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id || null,
                amount: totalPrice,
                currency: 'INR',
                method: 'razorpay',
                status: 'success',
                email: businessInfo?.email || '',
                contact: businessInfo?.phone || '',
                fee: (totalPrice * 0.02),
                tax: (totalPrice * 0.18),
                created_at: new Date().toISOString()
              }]);

            if (transactionError) {
              console.error('Error creating transaction record:', transactionError);
              // Continue with payment completion even if transaction record fails
            }

            // Record coupon usage if coupon was applied
            if (appliedCoupon) {
              try {
                await recordCouponUsage(appliedCoupon.coupon.id, userId, bookingId);
              } catch (couponError) {
                console.error('Error recording coupon usage:', couponError);
                // Don't block the flow if coupon usage recording fails
              }
            }

            // Send confirmation email
            try {
              await supabase.functions.invoke('send-payment-email', {
                body: { bookingId }
              });
            } catch (emailError) {
              console.error('Error sending confirmation email:', emailError);
              // Don't block the flow if email sending fails
            }

            // Complete payment and redirect
            onPaymentComplete(bookingId);
            // Don't reset states here as we're redirecting anyway
          } catch (error) {
            console.error('Error processing successful payment:', error);
            alert('Your payment was successful, but we encountered an issue updating our records. Our team will resolve this shortly. Payment ID: ' + response.razorpay_payment_id);
            navigate('/dashboard');
          }
        },
        prefill: {
          name: businessInfo?.businessName || '',
          email: businessInfo?.email || '',
          contact: businessInfo?.phone || '',
        },
        notes: {
          booking_id: bookingId,
          business_name: businessInfo?.businessName || '',
        },
        theme: {
          color: '#23044b'
        },
        modal: {
          ondismiss: function() {
            handlePaymentFailure({ error: 'Payment cancelled by user' });
          }
        },
        // Add this to disable QR code and specify allowed payment methods
        method: {
          netbanking: true,
          card: true,
          upi: true,
          wallet: true,
          qr: false // Explicitly disable QR code
        }
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.open();

    } catch (error) {
      console.error('Error initiating payment:', error);
      alert('Failed to initiate payment. Please try again or contact support.');
      resetPaymentState();
      navigate('/dashboard');
    }
  };

  // Reset states when component unmounts
  useEffect(() => {
    return () => {
      resetPaymentState();
    };
  }, []);

  return (
    <div className="relative min-h-full">
      <div className="space-y-6 pb-24">
        <div className="bg-white rounded-lg shadow-sm border divide-y">
          {cartItems.map((item) => {
            const startDate = new Date(item.dateRange.startDate!);
            const endDate = new Date(item.dateRange.endDate!);
            const daysDiff = differenceInDays(endDate, startDate) + 1;

            // Calculate price based on package type
            const packageType = businessInfo?.packageType || '';
            const isFixedPricePackage = packageType === 'fixed-price';
            const isPerScreenPackage = packageType === 'per-screen';

            let itemTotal = 0;

            if (isFixedPricePackage) {
              // Fixed price: ₹12,000 per zone (regardless of screen count)
              itemTotal = 12000;
            } else if (isPerScreenPackage) {
              // ₹500 per screen
              itemTotal = item.selectedScreens.length * 500;
            } else {
              // Fallback to old calculation if no package type
              const dailyRate = item.zone.price_year / 365;
              itemTotal = dailyRate * daysDiff;
            }

            return (
              <div key={item.zone.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-lg font-semibold text-gray-900">{item.zone.name}</h3>
                      {packageType && (
                        <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${
                          isFixedPricePackage ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {isFixedPricePackage ? 'Fixed Price' : 'Per Screen'}
                        </span>
                      )}
                    </div>
                    <p className="mt-1 text-sm text-gray-500">{item.zone.description}</p>

                    {/* Date range */}
                    <div className="mt-2 text-sm text-gray-700 flex items-center">
                      <Package className="h-4 w-4 mr-1 text-gray-500" />
                      {format(item.dateRange.startDate!, 'MMM d, yyyy')} -{' '}
                      {format(item.dateRange.endDate!, 'MMM d, yyyy')}
                      <span className="ml-2 text-gray-500">
                        ({Math.round(differenceInMonths(new Date(item.dateRange.endDate!), new Date(item.dateRange.startDate!)) / 12)}-year commitment)
                      </span>
                    </div>

                    {/* Selected screens */}
                    <div className="mt-2 text-sm text-gray-700 flex items-center">
                      <Monitor className="h-4 w-4 mr-1 text-gray-500" />
                      <span className="font-medium">
                        {isFixedPricePackage
                          ? '~25 screens included'
                          : `${item.selectedScreens.length} screens selected`
                        }
                      </span>
                    </div>

                    {/* Price calculation */}
                    <div className="mt-2 text-sm font-medium text-gray-900">
                      {isFixedPricePackage ? (
                        <>
                          Zone price: <span className="font-bold">{formatPrice(12000)}</span>
                          <span className="text-gray-500 ml-2">(includes ~25 screens)</span>
                        </>
                      ) : isPerScreenPackage ? (
                        <>
                          {item.selectedScreens.length} screens × {formatPrice(500)} =
                          <span className="font-bold ml-1">{formatPrice(itemTotal)}</span>
                        </>
                      ) : (
                        <>
                          {formatPrice(item.zone.price_year)} / year × {(daysDiff / 365).toFixed(2)} = {formatPrice(itemTotal)}
                        </>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => onRemoveItem(item.zone.id)}
                    className="ml-4 text-gray-400 hover:text-gray-500"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Coupon Input */}
        <CouponInput
          userId={userId}
          cartTotal={originalPrice}
          appliedCoupon={appliedCoupon}
          onCouponApplied={handleCouponApplied}
          onCouponRemoved={handleCouponRemoved}
        />

        {/* Price Summary */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <p>Subtotal</p>
              <p>{formatPrice(originalPrice)}</p>
            </div>

            {appliedCoupon && (
              <div className="flex items-center justify-between text-sm text-green-600">
                <p>Discount ({appliedCoupon.coupon.code})</p>
                <p>-{formatPrice(discountAmount)}</p>
              </div>
            )}

            <div className="border-t pt-2">
              <div className="flex items-center justify-between text-base font-medium text-gray-900">
                <p>Total</p>
                <p>{formatPrice(totalPrice)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="fixed inset-x-0 bottom-0 bg-white border-t shadow-lg z-50 safe-area-inset-bottom">
        <div className="max-w-7xl mx-auto p-4">
          <div className="flex justify-between items-center gap-4">
            <button
              onClick={() => {
                resetPaymentState();
                navigate('/dashboard');
              }}
              disabled={isProcessing || hasInitiatedPayment}
              className="flex-1 rounded-md bg-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Back
            </button>
            <button
              onClick={handlePayment}
              disabled={isProcessing || hasInitiatedPayment}
              className="flex-1 rounded-md bg-purple-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {(isProcessing || hasInitiatedPayment) ? 'Processing...' : `Pay ${formatPrice(totalPrice)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
