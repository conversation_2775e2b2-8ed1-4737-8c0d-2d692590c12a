import React, { useState } from 'react';
import type { Zone } from '../types';

const mockZones: Zone[] = [
  {
    id: '1',
    name: 'Zone A',
    description: 'Prime location near entrance',
    price: 100,
    status: 'available',
    image: 'https://images.unsplash.com/photo-1497366216548-37526070297c',
    slotDuration: 60,
  },
  {
    id: '2',
    name: 'Zone B',
    description: 'Central area with high foot traffic',
    price: 80,
    status: 'available',
    image: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2',
    slotDuration: 60,
  },
];

export default function TimeSlots() {
  const [selectedZone, setSelectedZone] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return `${hour}:00`;
  });

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Manage Time Slots</h1>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 mb-6">
          <div>
            <label htmlFor="zone" className="block text-sm font-medium text-gray-700">
              Select Zone
            </label>
            <select
              id="zone"
              value={selectedZone}
              onChange={(e) => setSelectedZone(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">Select a zone</option>
              {mockZones.map((zone) => (
                <option key={zone.id} value={zone.id}>
                  {zone.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700">
              Select Date
            </label>
            <input
              type="date"
              id="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            />
          </div>
        </div>

        {selectedZone && (
          <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
              <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50">
                {timeSlots.map((time) => (
                  <div
                    key={time}
                    className="relative flex items-center justify-center"
                  >
                    <input
                      type="checkbox"
                      id={`slot-${time}`}
                      className="peer sr-only"
                    />
                    <label
                      htmlFor={`slot-${time}`}
                      className="flex items-center justify-center w-full p-2 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-primary-600 peer-checked:text-primary-600"
                    >
                      {time}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Reset
              </button>
              <button
                type="button"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save Changes
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}