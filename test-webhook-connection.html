<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Webhook Connection</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      cursor: pointer;
      margin-right: 10px;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      max-height: 300px;
      overflow: auto;
    }
  </style>
</head>
<body>
  <h1>Test Webhook Connection</h1>
  <p>Use this tool to test if your webhook endpoint is accessible.</p>
  
  <div class="form-group">
    <label for="supabaseKey">Supabase Anon Key:</label>
    <input type="text" id="supabaseKey" placeholder="Enter your Supabase anon key" />
  </div>
  
  <button id="testGetButton">Test GET Request</button>
  <button id="testPostButton">Test POST Request</button>
  
  <div id="result" class="result">
    <h3>Results will appear here</h3>
  </div>
  
  <h2>Razorpay Webhook Setup Guide</h2>
  <ol>
    <li>Log in to your <a href="https://dashboard.razorpay.com/" target="_blank">Razorpay Dashboard</a></li>
    <li>Go to Settings > Webhooks</li>
    <li>Click on "Add New Webhook"</li>
    <li>Enter the following details:
      <ul>
        <li><strong>URL:</strong> https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook</li>
        <li><strong>Secret:</strong> 59KSN@ww3wAKZEc</li>
        <li><strong>Events:</strong> Select at least "payment.authorized" and "payment.captured"</li>
      </ul>
    </li>
    <li>Click "Create Webhook"</li>
  </ol>
  
  <h2>Verify Webhook in Razorpay</h2>
  <ol>
    <li>After creating the webhook, click on "Test" next to your webhook</li>
    <li>Select an event type (e.g., payment.captured)</li>
    <li>Click "Send Test Webhook"</li>
    <li>Check the response status in Razorpay dashboard</li>
  </ol>
  
  <script>
    // Test GET request
    document.getElementById('testGetButton').addEventListener('click', async () => {
      const supabaseKey = document.getElementById('supabaseKey').value.trim();
      const resultDiv = document.getElementById('result');
      
      if (!supabaseKey) {
        resultDiv.innerHTML = '<p style="color: red;">Please enter your Supabase Key</p>';
        return;
      }
      
      resultDiv.innerHTML = '<p>Testing GET request to webhook endpoint...</p>';
      
      try {
        // Add a random booking ID for testing
        const testBookingId = 'test-' + Math.random().toString(36).substring(2, 10);
        
        const response = await fetch(`https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook?booking_id=${testBookingId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${supabaseKey}`
          }
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
          responseData = JSON.parse(responseText);
        } catch (e) {
          responseData = responseText;
        }
        
        resultDiv.innerHTML = `
          <h3>GET Request Result</h3>
          <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
          <p>${response.ok ? '✅ Webhook endpoint is accessible via GET!' : '❌ Error accessing webhook endpoint via GET'}</p>
        `;
      } catch (error) {
        resultDiv.innerHTML = `
          <h3>GET Request Error</h3>
          <p style="color: red;">Error: ${error.message}</p>
          <p>❌ Could not connect to webhook endpoint. Check if the function is deployed and accessible.</p>
        `;
      }
    });
    
    // Test POST request
    document.getElementById('testPostButton').addEventListener('click', async () => {
      const supabaseKey = document.getElementById('supabaseKey').value.trim();
      const resultDiv = document.getElementById('result');
      
      if (!supabaseKey) {
        resultDiv.innerHTML = '<p style="color: red;">Please enter your Supabase Key</p>';
        return;
      }
      
      resultDiv.innerHTML = '<p>Testing POST request to webhook endpoint...</p>';
      
      try {
        // Create a simple test payload
        const testPayload = {
          event: 'test_event',
          payload: {
            payment: {
              entity: {
                id: 'test_payment_' + Math.random().toString(36).substring(2, 10),
                status: 'captured'
              }
            }
          }
        };
        
        const response = await fetch('https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${supabaseKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testPayload)
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
          responseData = JSON.parse(responseText);
        } catch (e) {
          responseData = responseText;
        }
        
        resultDiv.innerHTML = `
          <h3>POST Request Result</h3>
          <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
          <p>${response.ok ? '✅ Webhook endpoint is accessible via POST!' : '❌ Error accessing webhook endpoint via POST'}</p>
        `;
      } catch (error) {
        resultDiv.innerHTML = `
          <h3>POST Request Error</h3>
          <p style="color: red;">Error: ${error.message}</p>
          <p>❌ Could not connect to webhook endpoint. Check if the function is deployed and accessible.</p>
        `;
      }
    });
  </script>
</body>
</html>
