import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Map, 
  BookOpen,
  TrendingUp,
  Clock,
} from 'lucide-react';
import { supabase } from '../supabaseClient';

interface DashboardStats {
  totalZones: number;
  pendingBookings: number;
  approvedBookings: number;
  cancelledBookings: number;
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalZones: 0,
    pendingBookings: 0,
    approvedBookings: 0,
    cancelledBookings: 0,
  });

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // Fetch total zones
      const { data: zonesData, error: zonesError } = await supabase
        .from('zones')
        .select('id', { count: 'exact' });

      if (zonesError) throw zonesError;

      // Fetch bookings with different statuses
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('status');

      if (bookingsError) throw bookingsError;

      // Calculate booking stats
      const pendingBookings = bookingsData.filter(booking => booking.status === 'Pending').length;
      const approvedBookings = bookingsData.filter(booking => booking.status === 'Approved').length;
      const cancelledBookings = bookingsData.filter(booking => booking.status === 'Cancelled').length;

      setStats({
        totalZones: zonesData.length,
        pendingBookings,
        approvedBookings,
        cancelledBookings,
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    }
  };

  const statsConfig = [
    {
      id: 1,
      name: 'Total Zones',
      value: stats.totalZones.toString(),
      icon: Map,
      color: 'bg-blue-500',
    },
    {
      id: 2,
      name: 'Pending Bookings',
      value: stats.pendingBookings.toString(),
      icon: Clock,
      color: 'bg-yellow-500',
    },
    {
      id: 3,
      name: 'Approved Bookings',
      value: stats.approvedBookings.toString(),
      icon: TrendingUp,
      color: 'bg-green-500',
    },
    {
      id: 4,
      name: 'Cancelled Bookings',
      value: stats.cancelledBookings.toString(),
      icon: BookOpen,
      color: 'bg-red-500',
    },
  ];

  const quickLinks = [
    {
      name: 'Manage Zones',
      description: 'Add, edit, or remove booking zones',
      icon: Map,
      to: '/zones',
      color: 'bg-blue-500',
    },
    {
      name: 'View Bookings',
      description: 'Manage booking requests and approvals',
      icon: BookOpen,
      to: '/bookings',
      color: 'bg-purple-500',
    },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statsConfig.map((stat) => (
          <div
            key={stat.id}
            className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
          >
            <dt>
              <div className={`absolute rounded-md p-3 ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                {stat.name}
              </p>
            </dt>
            <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
              <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
            </dd>
          </div>
        ))}
      </div>

      {/* Quick Links Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
        {quickLinks.map((link) => (
          <Link
            key={link.name}
            to={link.to}
            className="relative group bg-white p-6 shadow rounded-lg hover:shadow-lg transition-shadow duration-200"
          >
            <div
              className={`inline-flex p-3 rounded-lg ${link.color} text-white`}
            >
              <link.icon className="h-6 w-6" aria-hidden="true" />
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                {link.name}
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                {link.description}
              </p>
            </div>
            <span
              className="absolute inset-0 rounded-lg ring-2 ring-offset-2 ring-primary-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              aria-hidden="true"
            />
          </Link>
        ))}
      </div>
    </div>
  );
}
