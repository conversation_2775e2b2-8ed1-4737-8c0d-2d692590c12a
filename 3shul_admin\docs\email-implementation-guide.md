# Email Implementation Guide

## Current Implementation

The current email functionality in the application is a simulation. When a booking is approved:

1. The system logs the email in the `email_logs` table in Supabase
2. The UI shows that an email would be sent
3. No actual email is sent to the customer or admin

## Implementing Real Email Sending

To implement actual email sending, you have several options:

### Option 1: Supabase Edge Functions

Supabase Edge Functions allow you to create serverless functions that can send emails:

1. Create a new Edge Function in your Supabase project:

```bash
supabase functions new send-email
```

2. Install the required dependencies in the function:

```bash
cd supabase/functions/send-email
npm install nodemailer
```

3. Implement the email sending logic in the function:

```typescript
// supabase/functions/send-email/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import nodemailer from 'npm:nodemailer'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { emailData, bookingId, emailConfig, adminEmail } = await req.json()
    
    // Create transporter
    const transporter = nodemailer.createTransport(emailConfig)

    // Send email
    await transporter.sendMail({
      from: `"3Shul Admin" <${adminEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
    })

    // Log email in database
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    await supabaseClient.from('email_logs').insert({
      recipient: emailData.to,
      subject: emailData.subject,
      booking_id: bookingId,
      status: 'success',
      sent_at: new Date().toISOString(),
    })

    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    // Log error in database
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    try {
      const { emailData, bookingId } = await req.json()
      await supabaseClient.from('email_logs').insert({
        recipient: emailData.to,
        subject: emailData.subject,
        booking_id: bookingId,
        status: 'failed',
        error_message: error.message,
        sent_at: new Date().toISOString(),
      })
    } catch (e) {
      // If we can't parse the request, just log a generic error
      console.error('Error logging email failure:', e)
    }

    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
```

4. Deploy the function:

```bash
supabase functions deploy send-email
```

5. Update the `sendEmail` function in `src/utils/emailService.ts` to call this Edge Function:

```typescript
export const sendEmail = async (emailData: EmailData, bookingId: string | null = null): Promise<boolean> => {
  if (!emailConfig || !adminEmail) {
    console.error('Email configuration not set');
    return false;
  }

  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        emailData,
        bookingId,
        emailConfig,
        adminEmail
      }
    });

    if (error) throw error;
    
    return data.success;
  } catch (error: any) {
    console.error('Error sending email:', error);
    return false;
  }
};
```

### Option 2: Third-Party Email Service

You can use a third-party email service like SendGrid, Mailgun, or Amazon SES:

1. Sign up for an account with the email service
2. Get your API key
3. Create a backend API endpoint that uses their SDK to send emails
4. Update the `sendEmail` function to call your API endpoint

Example with SendGrid:

```typescript
// Backend API endpoint
app.post('/api/send-email', async (req, res) => {
  const { emailData, bookingId } = req.body;
  
  try {
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    
    await sgMail.send({
      to: emailData.to,
      from: '<EMAIL>',
      subject: emailData.subject,
      html: emailData.html,
    });
    
    // Log to database
    // ...
    
    res.json({ success: true });
  } catch (error) {
    // Log error to database
    // ...
    
    res.status(500).json({ error: error.message });
  }
});
```

### Option 3: Email Service in Supabase Database Functions

You can create a PostgreSQL function in Supabase that sends emails using the `pg_net` extension:

1. Enable the `pg_net` extension in your Supabase project
2. Create a function that sends emails using HTTP requests to an email API
3. Call this function from your application code

## Security Considerations

1. **Never store email credentials in client-side code**
2. Use environment variables for API keys and credentials
3. Implement rate limiting to prevent abuse
4. Validate email addresses before sending
5. Use HTTPS for all API calls

## Testing Email Functionality

1. Use a service like Mailtrap for testing emails in development
2. Create test accounts with your email service provider
3. Implement a "test mode" that logs emails instead of sending them

## Monitoring and Troubleshooting

1. Use the `email_logs` table to track email status
2. Implement error handling and retries for failed emails
3. Set up alerts for high failure rates
4. Monitor email delivery rates and bounces

## Additional Resources

- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Nodemailer Documentation](https://nodemailer.com/about/)
- [SendGrid Documentation](https://docs.sendgrid.com/)
- [Mailgun Documentation](https://documentation.mailgun.com/)
