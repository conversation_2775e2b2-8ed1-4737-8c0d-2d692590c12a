<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Update Booking Status</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, button {
      padding: 8px;
      width: 100%;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .result {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <h1>Update Booking Payment Status</h1>
  <p>Use this form to manually update a booking's payment status to 'success'.</p>
  
  <div class="form-group">
    <label for="supabaseUrl">Supabase URL:</label>
    <input type="text" id="supabaseUrl" value="https://xirnspqtaoyzsqlbbhcl.supabase.co" />
  </div>
  
  <div class="form-group">
    <label for="supabaseKey">Supabase Anon Key:</label>
    <input type="text" id="supabaseKey" placeholder="Enter your Supabase anon key" />
  </div>
  
  <div class="form-group">
    <label for="bookingId">Booking ID:</label>
    <input type="text" id="bookingId" placeholder="Enter the booking ID" />
  </div>
  
  <button id="updateButton">Update Booking Status to Success</button>
  
  <div id="result" class="result"></div>
  
  <script>
    document.getElementById('updateButton').addEventListener('click', async function() {
      const supabaseUrl = document.getElementById('supabaseUrl').value;
      const supabaseKey = document.getElementById('supabaseKey').value;
      const bookingId = document.getElementById('bookingId').value;
      const resultDiv = document.getElementById('result');
      
      if (!supabaseUrl || !supabaseKey || !bookingId) {
        resultDiv.textContent = 'Please fill in all fields';
        resultDiv.className = 'result error';
        resultDiv.style.display = 'block';
        return;
      }
      
      try {
        // Initialize Supabase client
        const supabase = supabase.createClient(supabaseUrl, supabaseKey);
        
        // Update booking status
        const { data, error } = await supabase
          .from('bookings')
          .update({
            payment_status: 'success',
            status: 'Approved'
          })
          .eq('id', bookingId);
        
        if (error) {
          resultDiv.textContent = `Error: ${error.message}`;
          resultDiv.className = 'result error';
        } else {
          resultDiv.textContent = `Booking ${bookingId} successfully updated to success status!`;
          resultDiv.className = 'result success';
        }
      } catch (error) {
        resultDiv.textContent = `Unexpected error: ${error.message}`;
        resultDiv.className = 'result error';
      }
      
      resultDiv.style.display = 'block';
    });
  </script>
</body>
</html>
