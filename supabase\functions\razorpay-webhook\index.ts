// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Import required dependencies
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { createHmac } from "crypto";

// Define types for TypeScript
type WebhookPayload = {
  event: string;
  payload?: {
    payment?: {
      entity: {
        id: string;
        status: string;
      };
    };
  };
  entity?: {
    id: string;
    status: string;
  };
};

// Function to verify Razorpay webhook signature
function verifyRazorpaySignature(payload: string, signature: string, secret: string): boolean {
  try {
    const expectedSignature = createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    return expectedSignature === signature;
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

// Main webhook handler
serve(async (req) => {
  const WEBHOOK_SECRET = 'CdJjwfFuKSdqi3@';
  
  // Get environment variables
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

  // Initialize Supabase client
  const supabase = createClient(supabaseUrl, supabaseKey);

  console.log('Webhook request received');
  console.log('Method:', req.method);
  console.log('Headers:', [...req.headers.entries()]);

  try {
    if (req.method === 'POST') {
      const rawBody = await req.text();
      const signature = req.headers.get('x-razorpay-signature');

      console.log('Raw body length:', rawBody.length);
      console.log('Signature:', signature);

      // Verify signature
      if (!signature) {
        console.error('Missing Razorpay signature');
        return new Response(
          JSON.stringify({ success: false, error: 'Missing signature' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        );
      }

      const isValid = verifyRazorpaySignature(rawBody, signature, WEBHOOK_SECRET);
      if (!isValid) {
        console.error('Invalid signature');
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid signature' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        );
      }

      // Parse webhook payload
      const payload = JSON.parse(rawBody) as WebhookPayload;
      console.log('Webhook payload:', payload);

      // Extract payment information
      const paymentEntity = payload.payload?.payment?.entity || payload.entity;
      if (!paymentEntity) {
        throw new Error('Invalid payload structure');
      }

      const paymentId = paymentEntity.id;
      const paymentStatus = paymentEntity.status;

      console.log(`Processing payment ${paymentId} with status ${paymentStatus}`);

      // Log webhook data first
      await supabase.from('webhook_logs').insert({
        webhook_source: 'razorpay',
        webhook_event: payload.event,
        webhook_payload: payload,
        payment_id: paymentId,
        payment_status: paymentStatus,
        created_at: new Date().toISOString()
      });

      // Map Razorpay status to your system status
      const bookingStatus = paymentStatus === 'captured' ? 'Approved' : 
                          paymentStatus === 'failed' ? 'Failed' : 'Pending';

      // Find and update booking
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select('id, status')
        .eq('payment_id', paymentId)
        .single();

      if (bookingError) {
        console.error('Booking not found:', bookingError);
        throw new Error(`Booking not found for payment ${paymentId}`);
      }

      // Update booking status
      const { error: updateError } = await supabase
        .from('bookings')
        .update({
          payment_status: paymentStatus,
          status: bookingStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', booking.id);

      if (updateError) {
        console.error('Error updating booking:', updateError);
        throw new Error('Failed to update booking status');
      }

      // Update transaction in background
      try {
        await supabase
          .from('transactions')
          .update({
            status: paymentStatus,
            webhook_event: payload.event,
            webhook_payload: payload,
            updated_at: new Date().toISOString()
          })
          .eq('razorpay_payment_id', paymentId);
      } catch (transactionError) {
        console.error('Error updating transaction:', transactionError);
        // Don't throw error here, continue processing
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Webhook processed successfully',
          paymentId,
          status: paymentStatus
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Handle GET requests (health checks)
    if (req.method === 'GET') {
      const url = new URL(req.url);
      if (url.searchParams.get('test_env') === 'true') {
        return new Response(
          JSON.stringify({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            config: {
              supabase_url: !!supabaseUrl,
              supabase_key: !!supabaseKey,
              webhook_secret: !!WEBHOOK_SECRET
            }
          }),
          { 
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Log error to webhook_logs
    try {
      await supabase.from('webhook_logs').insert({
        webhook_source: 'razorpay',
        webhook_event: 'error',
        webhook_payload: { error: error.message, stack: error.stack },
        payment_status: 'error',
        created_at: new Date().toISOString()
      });
    } catch (logError) {
      console.error('Error logging to webhook_logs:', logError);
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
});
