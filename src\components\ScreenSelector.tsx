import { useState, useEffect } from 'react';
import { Screen, Zone, PackageType } from '../types';
import { Plus, Minus, Check, AlertCircle, Monitor, Search, Filter } from 'lucide-react';
import { formatPrice } from '../utils';

interface ScreenSelectorProps {
  zone: Zone;
  packageType: PackageType;
  selectedScreens: Screen[];
  onScreensSelected: (screens: Screen[]) => void;
  minScreens?: number;
}

export function ScreenSelector({
  zone,
  packageType,
  selectedScreens,
  onScreensSelected,
  minScreens = 25
}: ScreenSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [availableScreens, setAvailableScreens] = useState<Screen[]>([]);
  const [filteredScreens, setFilteredScreens] = useState<Screen[]>([]);

  // Initialize available screens from zone
  useEffect(() => {
    if (zone.screens) {
      const screens = zone.screens.filter(screen => screen.isavailable);
      setAvailableScreens(screens);
      setFilteredScreens(screens);
    }
  }, [zone]);

  // Filter screens based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredScreens(availableScreens);
    } else {
      const filtered = availableScreens.filter(
        screen =>
          screen.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          screen.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredScreens(filtered);
    }
  }, [searchTerm, availableScreens]);

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      // Deselect all
      onScreensSelected([]);
    } else {
      // Select all available screens
      onScreensSelected(availableScreens);
    }
    setSelectAll(!selectAll);
  };

  // Toggle selection of a single screen
  const toggleScreenSelection = (screen: Screen) => {
    const isSelected = selectedScreens.some(s => s.id === screen.id);

    if (isSelected) {
      // Remove screen from selection
      onScreensSelected(selectedScreens.filter(s => s.id !== screen.id));
    } else {
      // Add screen to selection
      onScreensSelected([...selectedScreens, screen]);
    }
  };

  // Calculate price based on package type
  const calculatePrice = () => {
    if (packageType === 'fixed-price') {
      // Base price for fixed package is ₹12,000
      const basePrice = 12000;

      // If more than 25 screens, price increases proportionally
      if (selectedScreens.length > minScreens) {
        const extraScreens = selectedScreens.length - minScreens;
        const extraCost = (basePrice / minScreens) * extraScreens;
        return basePrice + extraCost;
      }

      return basePrice;
    } else if (packageType === 'per-screen') {
      // ₹500 per screen
      return selectedScreens.length * 500;
    }

    return 0;
  };

  const isFixedPricePackage = packageType === 'fixed-price';
  const isPerScreenPackage = packageType === 'per-screen';
  const totalPrice = calculatePrice();
  const isMinScreensMet = selectedScreens.length >= minScreens;

  return (
    <div className="space-y-6">
      {/* Package info banner */}
      <div className={`p-4 rounded-lg ${isFixedPricePackage ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'}`}>
        <div className="flex items-start">
          <AlertCircle className={`h-5 w-5 mr-2 flex-shrink-0 ${isFixedPricePackage ? 'text-blue-500' : 'text-green-500'}`} />
          <div>
            <h3 className="text-sm font-medium mb-1">
              {isFixedPricePackage ? 'Fixed-Price Package' : 'Per-Screen Package'}
            </h3>
            <p className="text-sm text-gray-600">
              {isFixedPricePackage
                ? `Select at least ${minScreens} screens. Price increases proportionally for additional screens.`
                : `₹500 per screen with minimum ${minScreens} screens required.`}
            </p>
          </div>
        </div>
      </div>

      {/* Screen selection count and price */}
      <div className="flex flex-col sm:flex-row justify-between items-center p-4 bg-gray-50 rounded-lg border border-gray-200">
        <div className="mb-2 sm:mb-0 flex items-center">
          <Monitor className="h-5 w-5 text-purple-600 mr-2" />
          <div>
            <span className="text-sm font-medium text-gray-700">
              {selectedScreens.length} of {availableScreens.length} screens selected
            </span>
            {!isMinScreensMet && (
              <p className="text-xs text-red-600 mt-1">
                Please select at least {minScreens} screens
              </p>
            )}
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600">Total Price:</p>
          <p className="text-lg font-bold text-purple-700">
            {formatPrice(totalPrice)}
          </p>
        </div>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center bg-white p-4 rounded-lg border border-gray-200">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search screens by name or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          />
        </div>

        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={handleSelectAll}
            className="flex items-center px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 rounded-md border border-purple-200 hover:bg-purple-100"
          >
            {selectAll ? (
              <>
                <Minus className="h-4 w-4 mr-1" />
                Deselect All
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-1" />
                Select All
              </>
            )}
          </button>

          <button
            type="button"
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100"
          >
            <Filter className="h-4 w-4 mr-1" />
            Filter
          </button>
        </div>
      </div>

      {/* Screen list */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {filteredScreens.map(screen => {
          const isSelected = selectedScreens.some(s => s.id === screen.id);

          return (
            <div
              key={screen.id}
              onClick={() => toggleScreenSelection(screen)}
              className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-sm ${
                isSelected
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-purple-300'
              }`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center">
                    <Monitor className="h-4 w-4 text-gray-500 mr-1.5" />
                    <h4 className="font-medium text-gray-900">{screen.name}</h4>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{screen.location}</p>
                  <p className="text-xs text-gray-500 mt-1">Size: {screen.size}</p>
                </div>
                <div className={`h-5 w-5 rounded-full flex items-center justify-center ${
                  isSelected ? 'bg-purple-600' : 'border border-gray-300'
                }`}>
                  {isSelected && <Check className="h-3 w-3 text-white" />}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredScreens.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
          <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500 font-medium">No screens found matching your search criteria</p>
          <p className="text-gray-400 text-sm mt-1">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
}
