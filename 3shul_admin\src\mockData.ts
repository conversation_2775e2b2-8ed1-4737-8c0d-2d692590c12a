import type { City, Zone, Screen } from './types';

// Mock Cities Data
export const MOCK_CITIES: City[] = [
  {
    id: 'city-1',
    name: 'Mumbai',
    description: 'Financial capital of India with high-traffic commercial areas and premium shopping districts.',
    image_url: 'https://images.unsplash.com/photo-1570168007204-dfb528c6958f?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'city-2',
    name: 'Delhi',
    description: 'National capital with diverse commercial zones, government offices, and bustling markets.',
    image_url: 'https://images.unsplash.com/photo-1587474260584-136574528ed5?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'city-3',
    name: 'Bangalore',
    description: 'IT hub of India with modern office complexes, tech parks, and vibrant commercial areas.',
    image_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'city-4',
    name: 'Chennai',
    description: 'Major port city with industrial zones, automotive hubs, and traditional commercial areas.',
    image_url: 'https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'city-5',
    name: 'Hyderabad',
    description: 'Growing tech city with HITEC City, pharmaceutical companies, and modern commercial districts.',
    image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'city-6',
    name: 'Pune',
    description: 'Educational and IT hub with numerous colleges, tech companies, and commercial centers.',
    image_url: 'https://images.unsplash.com/photo-1595658658481-d53d3f999875?w=500',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

// Mock Zones Data
export const MOCK_ZONES: Zone[] = [
  // Mumbai Zones
  {
    id: 'zone-1',
    name: 'Premium Display Zone A',
    city_id: 'city-1',
    city_name: 'Mumbai',
    subZone: 'Colaba',
    pricePerYear: 1500000,
    description: 'High-traffic area perfect for product displays and exhibitions',
    imageUrl: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500',
    isAvailable: true
  },
  {
    id: 'zone-2',
    name: 'Business District Zone B',
    city_id: 'city-1',
    city_name: 'Mumbai',
    subZone: 'Bandra Kurla Complex',
    pricePerYear: 1800000,
    description: 'Corporate hub with high-end audience and business professionals',
    imageUrl: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500',
    isAvailable: true
  },
  {
    id: 'zone-3',
    name: 'Shopping Mall Zone C',
    city_id: 'city-1',
    city_name: 'Mumbai',
    subZone: 'Andheri West',
    pricePerYear: 1200000,
    description: 'Busy shopping area with diverse customer base',
    imageUrl: 'https://images.unsplash.com/photo-1519567241046-7f570eee3ce6?w=500',
    isAvailable: true
  },
  // Delhi Zones
  {
    id: 'zone-4',
    name: 'Central Business District',
    city_id: 'city-2',
    city_name: 'Delhi',
    subZone: 'Connaught Place',
    pricePerYear: 1600000,
    description: 'Prime commercial location with high footfall',
    imageUrl: 'https://images.unsplash.com/photo-1519567241046-7f570eee3ce6?w=500',
    isAvailable: true
  },
  {
    id: 'zone-5',
    name: 'Metro Station Hub',
    city_id: 'city-2',
    city_name: 'Delhi',
    subZone: 'Rajiv Chowk',
    pricePerYear: 1400000,
    description: 'High-traffic metro station with excellent visibility',
    imageUrl: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500',
    isAvailable: true
  },
  // Bangalore Zones
  {
    id: 'zone-6',
    name: 'Tech Park Zone',
    city_id: 'city-3',
    city_name: 'Bangalore',
    subZone: 'Electronic City',
    pricePerYear: 1300000,
    description: 'IT professionals and tech companies concentrated area',
    imageUrl: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=500',
    isAvailable: true
  },
  {
    id: 'zone-7',
    name: 'Commercial Street',
    city_id: 'city-3',
    city_name: 'Bangalore',
    subZone: 'MG Road',
    pricePerYear: 1100000,
    description: 'Popular shopping and entertainment district',
    imageUrl: 'https://images.unsplash.com/photo-1519567241046-7f570eee3ce6?w=500',
    isAvailable: true
  }
];

// Mock Screens Data (Basic set - will be extended)
export const MOCK_SCREENS: Screen[] = [
  // Zone 1 (Mumbai - Colaba) Screens
  {
    id: 'screen-1',
    name: 'Screen A1',
    screen_number: 'SCR-001',
    zone_id: 'zone-1',
    zone_name: 'Premium Display Zone A',
    address: 'Colaba Causeway, Near Taj Hotel',
    location: 'Main Entrance',
    size: '55"',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'screen-2',
    name: 'Screen A2',
    screen_number: 'SCR-002',
    zone_id: 'zone-1',
    zone_name: 'Premium Display Zone A',
    address: 'Colaba Market, Food Court Area',
    location: 'Food Court',
    size: '42"',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'screen-3',
    name: 'Screen A3',
    screen_number: 'SCR-003',
    zone_id: 'zone-1',
    zone_name: 'Premium Display Zone A',
    address: 'Gateway of India, Waiting Area',
    location: 'Waiting Area',
    size: '55"',
    isavailable: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

// Generate additional screens for each zone to reach ~25 per zone
const generateAdditionalScreens = () => {
  const additionalScreens: Screen[] = [];
  
  MOCK_ZONES.forEach((zone, zoneIndex) => {
    // Generate 22 more screens per zone (we already have 1-3 per zone)
    for (let i = 1; i <= 22; i++) {
      const screenId = `screen-${zoneIndex + 1}-${i + 10}`;
      const screenNumber = `SCR-${(zoneIndex * 25 + i + 10).toString().padStart(3, '0')}`;
      
      additionalScreens.push({
        id: screenId,
        name: `Screen ${zone.name.charAt(0)}${i + 10}`,
        screen_number: screenNumber,
        zone_id: zone.id,
        zone_name: zone.name,
        address: `${zone.subZone} Area ${i}, ${zone.city_name}`,
        location: `Location ${i}`,
        size: ['32"', '42"', '55"', '65"'][i % 4],
        isavailable: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      });
    }
  });
  
  return additionalScreens;
};

// Export all screens including additional ones
export const ALL_MOCK_SCREENS = [...MOCK_SCREENS, ...generateAdditionalScreens()];
