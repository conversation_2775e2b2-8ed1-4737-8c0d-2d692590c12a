# Product Context

## Problem Being Solved
Businesses need a streamlined way to book digital advertising space in premium locations listed on the3shul.com. Current solutions are often manual and inefficient.

## User Experience Goals
1. Quick and easy booking process without mandatory account logins.
2. Clear visualization of available zones and booking durations.
3. Simple video upload for advertising content.
4. Transparent pricing and secure Razorpay payment integration.
5. Automated booking confirmations and email notifications.
6. Centralized admin dashboard for managing zones, bookings, and user accounts.

## Key User Flows
1. **Booking Flow:** Browse zones → Select dates → Upload video → Complete payment → Booking confirmation.
2. **Admin Flow:** Login → Manage zones → Manage bookings → View transactions.

## Target Audience
- Small and medium businesses looking to advertise on the3shul.com
- Marketing agencies managing advertising campaigns.
- Event organizers needing commercial spaces for promotions.

## Business Model
- Commission on each booking made through the platform.
- Potential for premium listings or subscription models in the future.

## Database Schema
- **Users Table:** Manages admin users with email and password for backend access.
- **Zones Table:** Stores details of advertising zones including location, pricing, and availability.
- **Bookings Table:** Records booking information, linking users to zones and specifying booking dates and details.
- **Transactions Table:** Logs payment transactions made via Razorpay, associated with each booking.

## Backend Integration
- Supabase will be used as the backend service to manage the database, authentication, and potentially storage and serverless functions.
- Razorpay integration for secure payment processing.
- Automated email confirmations for bookings and transactions.
