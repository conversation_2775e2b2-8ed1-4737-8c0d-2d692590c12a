export interface User {
  id: string;
  email: string;
  name: string;
}

export interface City {
  id: string;
  name: string;
  description: string;
  image_url: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Screen {
  id: string;
  name: string;
  screen_number: string;
  zone_id: string;
  zone_name?: string;
  address: string;
  location: string;
  size: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Zone {
  id: string;
  name: string;
  city_id: string;
  city_name?: string;
  subZone: string;
  pricePerYear: number;
  description: string;
  imageUrl: string;
  isAvailable: boolean;
  screens?: Screen[];
}

export interface BookingZone {
  zone_id: string;
  zones: Zone;
}

export interface Booking {
  id: string;
  user_id: string;
  zone_id: string;
  start_date: string;
  end_date: string;
  listing_type: string;
  business_name: string;
  description: string;
  phone_number: string;
  email?: string; // Added email field
  video_url?: string;
  status: 'Pending' | 'Approved' | 'Fulfilled' | 'Cancelled';
  payment_id: string;
  created_at: string;
  updated_at: string;
  zones?: Zone;
  booking_zones?: BookingZone[];
  transactions?: Transaction[];
  users?: User;
}

export interface Transaction {
  id: string;
  razorpay_payment_id: string;
  razorpay_order_id?: string;
  amount: number;
  currency?: string;
  method?: string;
  status?: string;
  email?: string;
  contact?: string;
  fee?: number;
  tax?: number;
  created_at: string;
}

export interface Coupon {
  id: string;
  code: string;
  type: 'flat' | 'percentage';
  amount: number;
  max_discount?: number;
  min_cart_value: number;
  global_usage_limit?: number;
  per_user_usage_limit?: number;
  start_date: string;
  expiry_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CouponUsage {
  id: string;
  coupon_id: string;
  user_id: string;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
}

export interface CouponTier {
  id: string;
  coupon_id: string;
  usage_number: number;
  amount?: number;
  percentage?: number;
  created_at: string;
}

export interface CouponFormData {
  code: string;
  type: 'flat' | 'percentage';
  amount: number | '';
  max_discount: number | '';
  min_cart_value: number | '';
  global_usage_limit: number | '';
  per_user_usage_limit: number | '';
  start_date: string;
  expiry_date: string;
  is_active: boolean;
}

export interface EmailLog {
  id: string;
  recipient: string;
  subject: string;
  booking_id?: string;
  status: 'success' | 'failed';
  error_message?: string;
  sent_at: string;
  created_at: string;
}
