import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Power } from 'lucide-react';
import { supabase } from '../supabaseClient';
import { MOCK_CITIES } from '../mockData';

interface City {
  id: string;
  name: string;
  description: string;
  image_url: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

interface CityFormData {
  name: string;
  description: string;
  imageUrl: string;
  isAvailable: boolean;
}

const defaultFormData: CityFormData = {
  name: '',
  description: '',
  imageUrl: '',
  isAvailable: true
};

export default function Cities() {
  const [cities, setCities] = useState<City[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [formData, setFormData] = useState<CityFormData>(defaultFormData);
  const [previewImage, setPreviewImage] = useState<string>('');

  useEffect(() => {
    const fetchCities = async () => {
      try {
        const { data, error } = await supabase
          .from('cities')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching cities:', error);
          alert('Error loading cities from database. Please check your connection.');
          setCities([]);
        } else {
          setCities(data || []);
        }
      } catch (err) {
        console.error('Database connection error:', err);
        alert('Database connection error. Please check your connection.');
        setCities([]);
      }
    };

    fetchCities();
  }, []);

  const handleOpenModal = (city?: City) => {
    if (city) {
      setEditingCity(city);
      setFormData({
        name: city.name || '',
        description: city.description || '',
        imageUrl: city.image_url || '',
        isAvailable: city.isavailable ?? true,
      });
      setPreviewImage(city.image_url || '');
    } else {
      setEditingCity(null);
      setFormData(defaultFormData);
      setPreviewImage('');
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingCity(null);
    setFormData(defaultFormData);
    setPreviewImage('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const submissionData = {
      name: formData.name,
      description: formData.description,
      image_url: formData.imageUrl,
      isavailable: formData.isAvailable,
    };

    if (editingCity) {
      // Update existing city
      const { data, error } = await supabase
        .from('cities')
        .update(submissionData)
        .eq('id', editingCity.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating city:', error);
        alert('Error updating city. Please try again.');
      } else if (data) {
        setCities(prevCities => prevCities.map(city =>
          city.id === editingCity.id ? data : city
        ));
        console.log('City updated:', data);
      }
    } else {
      // Add new city
      const { data, error } = await supabase
        .from('cities')
        .insert([submissionData])
        .select()
        .single();

      if (error) {
        console.error('Error adding city:', error);
        alert('Error adding city. Please try again.');
      } else if (data) {
        setCities(prevCities => [data, ...prevCities]);
        console.log('City added:', data);
      }
    }

    handleCloseModal();
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this city? This will also delete all zones and screens in this city.')) {
      const { error } = await supabase
        .from('cities')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting city:', error);
        alert('Error deleting city. Please try again.');
      } else {
        setCities(prevCities => prevCities.filter(city => city.id !== id));
        console.log('City deleted:', id);
      }
    }
  };

  const toggleAvailability = async (id: string) => {
    const cityToUpdate = cities.find(city => city.id === id);
    if (!cityToUpdate) return;

    const newAvailability = !cityToUpdate.isavailable;

    const { error } = await supabase
      .from('cities')
      .update({ isavailable: newAvailability })
      .eq('id', id);

    if (error) {
      console.error('Error toggling availability:', error);
      alert('Error updating city availability. Please try again.');
    } else {
      setCities(prevCities => prevCities.map(city =>
        city.id === id ? { ...city, isavailable: newAvailability } : city
      ));
      console.log('City availability toggled:', id, newAvailability);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manage Cities</h1>
        <button
          onClick={() => handleOpenModal()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New City
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {cities.map((city) => (
          <div key={city.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="h-48 w-full overflow-hidden">
              <img
                src={city.image_url}
                alt={city.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-gray-900">{city.name}</h3>
                <button
                  onClick={() => toggleAvailability(city.id)}
                  className={`${
                    city.isavailable
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  } px-2 py-1 rounded-full text-xs font-medium inline-flex items-center`}
                >
                  <Power className="h-3 w-3 mr-1" />
                  {city.isavailable ? 'Active' : 'Inactive'}
                </button>
              </div>
              <p className="mt-2 text-sm text-gray-500">{city.description}</p>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => handleOpenModal(city)}
                  className="text-primary-600 hover:text-primary-900"
                >
                  <Pencil className="h-5 w-5" />
                </button>
                <button
                  onClick={() => handleDelete(city.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-2xl w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingCity ? 'Edit City' : 'Add New City'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      City Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <textarea
                      id="description"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                      Image URL
                    </label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        id="imageUrl"
                        value={formData.imageUrl}
                        onChange={(e) => {
                          setFormData({ ...formData, imageUrl: e.target.value });
                          setPreviewImage(e.target.value);
                        }}
                        className="flex-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    {previewImage && (
                      <div className="mt-2">
                        <img
                          src={previewImage}
                          alt="Preview"
                          className="h-32 w-full object-cover rounded-md"
                          onError={() => setPreviewImage('')}
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Availability
                    </label>
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isAvailable}
                          onChange={(e) => setFormData({ ...formData, isAvailable: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          City is available for selection
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCloseModal}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {editingCity ? 'Save Changes' : 'Add City'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
