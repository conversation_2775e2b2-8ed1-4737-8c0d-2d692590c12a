# Project Intelligence

## Critical Implementation Paths
- User authentication and authorization: `src/pages/Login.tsx`
- Zone management: `src/pages/Zones.tsx`
- Booking management: `src/pages/Bookings.tsx`

## User Preferences and Workflow
- The user prefers to use Visual Studio Code for development.
- The user prefers to use npm for package management.
- The user prefers to use Tailwind CSS for styling.

## Project-Specific Patterns
- Using UUIDs for primary keys in the database.
- Using timestamps for tracking creation and modification times.
- Using a consistent naming convention for components and files.

## Known Challenges
- Implementing secure user authentication and authorization.
- Integrating with a payment gateway for processing transactions.
- Ensuring the application is responsive and accessible.

## Evolution of Project Decisions
- Initially, the project was planned to use a different database technology, but PostgreSQL was chosen due to its robustness and scalability.
- The project initially used a different styling library, but Tailwind CSS was chosen due to its ease of use and customization.

## Tool Usage Patterns
- Using `list_files` to explore the project structure.
- Using `read_file` to examine the contents of files.
- Using `write_to_file` to create and modify files.
- Using `execute_command` to run commands in the terminal.
