# System Patterns

## Architecture
- Single Page Application (SPA) built with React and TypeScript on the frontend.
- Supabase for backend services including:
    - PostgreSQL database to manage zones, bookings, users, and transactions.
    - Authentication for admin users.
    - Storage for video uploads (potential future enhancement).
    - Serverless functions for backend logic (potential future enhancement).
- Razorpay for payment processing.
- Email service for automated notifications (potential future enhancement).

## Database Schema
- **Users Table:** Stores admin user credentials.
- **Zones Table:** Stores zone details (id, name, city, sub_zone, price, image, etc.).
- **Bookings Table:** Manages booking records, linking to users and zones, and storing booking details (dates, status, video URL, etc.).
- **Transactions Table:** Records transaction details for each booking (payment ID, amount, status, etc.).

## Key Components
1. **BookingForm**: Multi-step form for user bookings.
2. **ZoneList**: Displays available zones fetched from the database.
3. **Dashboard**: Admin dashboard to manage zones and bookings.
4. **AuthContext**: Manages authentication state using Supabase Auth.
5. **PaymentStatus**: Handles payment processing and status updates using Razorpay.
6. **Supabase Client**:  Interface to interact with the Supabase backend.

## State Management
- React Context API for global state management (e.g., auth state).
- Local component state for form inputs and UI interactions.
- Data fetching and state updates using React Hooks.

## Routing Structure
- /login - Admin login page.
- /dashboard - Admin dashboard (protected route).
- /book - Booking form for users (protected route in future if needed).
- / - Redirects to login.

## UI Patterns
- Tailwind CSS for responsive and consistent styling.
- Headless UI for accessible and customizable UI components.
- Stepper component for multi-step forms.
- Date-fns for date and time handling.
- Lucide React icons for UI icons.
