# System Patterns

## System Architecture
- The system follows a layered architecture, with distinct layers for presentation, business logic, and data access.
- The presentation layer is implemented using React.
- The business logic layer is implemented using TypeScript.
- The data access layer interacts with a PostgreSQL database.

## Key Technical Decisions
- Using React for the presentation layer.
- Using TypeScript for the business logic layer.
- Using PostgreSQL as the database.
- Implementing user authentication and authorization using JWT.

## Design Patterns in Use
- Model-View-Controller (MVC)
- Data Access Object (DAO)
- Singleton
- Factory

## Component Relationships
- The `App` component is the root component of the application.
- The `Layout` component provides the basic layout for all pages.
- The `Dashboard` component displays key metrics and information.
- The `Zones` component allows administrators to manage zones.
- The `Bookings` component allows users to make and manage bookings.
