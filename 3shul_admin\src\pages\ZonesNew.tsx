import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Power, Monitor, ChevronDown, ChevronRight } from 'lucide-react';
import type { Zone, City, Screen } from '../types';
import { supabase } from '../supabaseClient';
import { MOCK_CITIES, MOCK_ZONES, ALL_MOCK_SCREENS } from '../mockData';

interface ZoneFormData {
  name: string;
  cityId: string;
  subZone: string;
  pricePerYear: number | '';
  description: string;
  imageUrl: string;
  isAvailable: boolean;
}

interface ScreenFormData {
  name: string;
  screenNumber: string;
  address: string;
  location: string;
  size: string;
  isAvailable: boolean;
}

const defaultFormData: ZoneFormData = {
  name: '',
  cityId: '',
  subZone: '',
  pricePerYear: '',
  description: '',
  imageUrl: '',
  isAvailable: true
};

const defaultScreenFormData: ScreenFormData = {
  name: '',
  screenNumber: '',
  address: '',
  location: '',
  size: '42"',
  isAvailable: true,
};

export default function Zones() {
  const [zones, setZones] = useState<Zone[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [screens, setScreens] = useState<Screen[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isScreenModalOpen, setIsScreenModalOpen] = useState(false);
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [editingScreen, setEditingScreen] = useState<Screen | null>(null);
  const [selectedZoneForScreens, setSelectedZoneForScreens] = useState<Zone | null>(null);
  const [formData, setFormData] = useState<ZoneFormData>(defaultFormData);
  const [screenFormData, setScreenFormData] = useState<ScreenFormData>(defaultScreenFormData);
  const [previewImage, setPreviewImage] = useState<string>('');
  const [expandedZones, setExpandedZones] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch cities
        const { data: citiesData, error: citiesError } = await supabase
          .from('cities')
          .select('*')
          .eq('isavailable', true)
          .order('name');

        if (citiesError) {
          console.error('Error fetching cities:', citiesError);
          alert('Error loading cities from database. Please check your connection.');
          setCities([]);
        } else {
          setCities(citiesData || []);
        }

        // Fetch zones with city information
        const { data: zonesData, error: zonesError } = await supabase
          .from('zones')
          .select(`
            id,
            name,
            city_id,
            city_name,
            sub_zone,
            price_year,
            description,
            image_url,
            isavailable,
            created_at,
            updated_at
          `)
          .order('created_at', { ascending: false });

        if (zonesError) {
          console.error('Error fetching zones:', zonesError);
          alert('Error loading zones from database. Please check your connection.');
          setZones([]);
        } else {
          // Transform data to match our interface
          const transformedZones = zonesData?.map(zone => ({
            id: zone.id,
            name: zone.name,
            city_id: zone.city_id,
            city_name: zone.city_name,
            subZone: zone.sub_zone,
            pricePerYear: zone.price_year,
            description: zone.description,
            imageUrl: zone.image_url,
            isAvailable: zone.isavailable,
            created_at: zone.created_at,
            updated_at: zone.updated_at
          })) || [];
          setZones(transformedZones);
        }

        // Fetch screens
        const { data: screensData, error: screensError } = await supabase
          .from('screens')
          .select('*')
          .order('created_at', { ascending: false });

        if (screensError) {
          console.error('Error fetching screens:', screensError);
          alert('Error loading screens from database. Please check your connection.');
          setScreens([]);
        } else {
          setScreens(screensData || []);
        }
      } catch (err) {
        console.error('Database connection error:', err);
        alert('Database connection error. Please check your connection.');
        setCities([]);
        setZones([]);
        setScreens([]);
      }
    };

    fetchData();
  }, []);

  // Zone management functions
  const handleOpenModal = (zone?: Zone) => {
    if (zone) {
      const formDataFromZone: ZoneFormData = {
        name: zone.name,
        cityId: zone.city_id,
        subZone: zone.subZone,
        pricePerYear: zone.pricePerYear,
        description: zone.description,
        imageUrl: zone.imageUrl,
        isAvailable: zone.isAvailable,
      };
      setEditingZone(zone);
      setFormData(formDataFromZone);
      setPreviewImage(zone.imageUrl);
    } else {
      setEditingZone(null);
      setFormData(defaultFormData);
      setPreviewImage('');
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingZone(null);
    setFormData(defaultFormData);
    setPreviewImage('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Find the selected city to get city name
    const selectedCity = cities.find(city => city.id === formData.cityId);

    const submissionData = {
      name: formData.name,
      city: selectedCity?.name || '',
      city_id: formData.cityId,
      city_name: selectedCity?.name || '',
      sub_zone: formData.subZone,
      price_year: Number(formData.pricePerYear),
      description: formData.description,
      image_url: formData.imageUrl,
      isavailable: formData.isAvailable,
    };

    if (editingZone) {
      // Update existing zone
      const { data, error } = await supabase
        .from('zones')
        .update(submissionData)
        .eq('id', editingZone.id)
        .select(`
          id,
          name,
          city_id,
          city_name,
          sub_zone,
          price_year,
          description,
          image_url,
          isavailable,
          created_at,
          updated_at
        `)
        .single();

      if (error) {
        console.error('Error updating zone:', error);
        alert('Error updating zone. Please try again.');
      } else if (data) {
        const transformedZone = {
          id: data.id,
          name: data.name,
          city_id: data.city_id,
          city_name: data.city_name,
          subZone: data.sub_zone,
          pricePerYear: data.price_year,
          description: data.description,
          imageUrl: data.image_url,
          isAvailable: data.isavailable,
          created_at: data.created_at,
          updated_at: data.updated_at
        };
        setZones(prevZones => prevZones.map(zone =>
          zone.id === editingZone.id ? transformedZone : zone
        ));
        console.log('Zone updated:', transformedZone);
      }
    } else {
      // Add new zone
      const { data, error } = await supabase
        .from('zones')
        .insert([submissionData])
        .select(`
          id,
          name,
          city_id,
          city_name,
          sub_zone,
          price_year,
          description,
          image_url,
          isavailable,
          created_at,
          updated_at
        `)
        .single();

      if (error) {
        console.error('Error adding zone:', error);
        alert('Error adding zone. Please try again.');
      } else if (data) {
        const transformedZone = {
          id: data.id,
          name: data.name,
          city_id: data.city_id,
          city_name: data.city_name,
          subZone: data.sub_zone,
          pricePerYear: data.price_year,
          description: data.description,
          imageUrl: data.image_url,
          isAvailable: data.isavailable,
          created_at: data.created_at,
          updated_at: data.updated_at
        };
        setZones(prevZones => [transformedZone, ...prevZones]);
        console.log('Zone added:', transformedZone);
      }
    }

    handleCloseModal();
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this zone? This will also delete all screens in this zone.')) {
      const { error } = await supabase
        .from('zones')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting zone:', error);
        alert('Error deleting zone. Please try again.');
      } else {
        // Remove zone and its screens from local state
        setZones(prevZones => prevZones.filter(zone => zone.id !== id));
        setScreens(prevScreens => prevScreens.filter(screen => screen.zone_id !== id));
        console.log('Zone and its screens deleted:', id);
      }
    }
  };

  const toggleAvailability = async (id: string) => {
    const zoneToUpdate = zones.find(zone => zone.id === id);
    if (!zoneToUpdate) return;

    const newAvailability = !zoneToUpdate.isAvailable;

    const { error } = await supabase
      .from('zones')
      .update({ isavailable: newAvailability })
      .eq('id', id);

    if (error) {
      console.error('Error toggling zone availability:', error);
      alert('Error updating zone availability. Please try again.');
    } else {
      setZones(prevZones => prevZones.map(zone =>
        zone.id === id ? { ...zone, isAvailable: newAvailability } : zone
      ));
      console.log('Zone availability toggled:', id, newAvailability);
    }
  };

  // Screen management functions
  const getScreensForZone = (zoneId: string): Screen[] => {
    return screens.filter(screen => screen.zone_id === zoneId);
  };

  const toggleZoneExpansion = (zoneId: string) => {
    setExpandedZones(prev => {
      const newSet = new Set(prev);
      if (newSet.has(zoneId)) {
        newSet.delete(zoneId);
      } else {
        newSet.add(zoneId);
      }
      return newSet;
    });
  };

  const handleOpenScreenModal = (zone: Zone, screen?: Screen) => {
    setSelectedZoneForScreens(zone);
    if (screen) {
      setEditingScreen(screen);
      setScreenFormData({
        name: screen.name,
        screenNumber: screen.screen_number,
        address: screen.address,
        location: screen.location,
        size: screen.size,
        isAvailable: screen.isavailable,
      });
    } else {
      setEditingScreen(null);
      setScreenFormData(defaultScreenFormData);
    }
    setIsScreenModalOpen(true);
  };

  const handleCloseScreenModal = () => {
    setIsScreenModalOpen(false);
    setEditingScreen(null);
    setSelectedZoneForScreens(null);
    setScreenFormData(defaultScreenFormData);
  };

  const handleScreenSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedZoneForScreens) return;

    const submissionData = {
      name: screenFormData.name,
      screen_number: screenFormData.screenNumber,
      zone_id: selectedZoneForScreens.id,
      zone_name: selectedZoneForScreens.name,
      address: screenFormData.address,
      location: screenFormData.location,
      size: screenFormData.size,
      isavailable: screenFormData.isAvailable,
    };

    if (editingScreen) {
      // Update existing screen
      const { data, error } = await supabase
        .from('screens')
        .update(submissionData)
        .eq('id', editingScreen.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating screen:', error);
        alert('Error updating screen. Please try again.');
      } else if (data) {
        setScreens(prevScreens => prevScreens.map(screen =>
          screen.id === editingScreen.id ? data : screen
        ));
        console.log('Screen updated:', data);
      }
    } else {
      // Add new screen
      const { data, error } = await supabase
        .from('screens')
        .insert([submissionData])
        .select()
        .single();

      if (error) {
        console.error('Error adding screen:', error);
        alert('Error adding screen. Please try again.');
      } else if (data) {
        setScreens(prevScreens => [data, ...prevScreens]);
        console.log('Screen added:', data);
      }
    }

    handleCloseScreenModal();
  };

  const handleDeleteScreen = async (screenId: string) => {
    if (window.confirm('Are you sure you want to delete this screen?')) {
      const { error } = await supabase
        .from('screens')
        .delete()
        .eq('id', screenId);

      if (error) {
        console.error('Error deleting screen:', error);
        alert('Error deleting screen. Please try again.');
      } else {
        setScreens(prevScreens => prevScreens.filter(screen => screen.id !== screenId));
        console.log('Screen deleted:', screenId);
      }
    }
  };

  const toggleScreenAvailability = async (screenId: string) => {
    const screenToUpdate = screens.find(screen => screen.id === screenId);
    if (!screenToUpdate) return;

    const newAvailability = !screenToUpdate.isavailable;

    const { error } = await supabase
      .from('screens')
      .update({ isavailable: newAvailability })
      .eq('id', screenId);

    if (error) {
      console.error('Error toggling screen availability:', error);
      alert('Error updating screen availability. Please try again.');
    } else {
      setScreens(prevScreens => prevScreens.map(screen =>
        screen.id === screenId ? { ...screen, isavailable: newAvailability } : screen
      ));
      console.log('Screen availability toggled:', screenId, newAvailability);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manage Zones & Screens</h1>
        <button
          onClick={() => handleOpenModal()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New Zone
        </button>
      </div>

      <div className="space-y-4">
        {zones.map((zone) => {
          const zoneScreens = getScreensForZone(zone.id);
          const isExpanded = expandedZones.has(zone.id);

          return (
            <div key={zone.id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* Zone Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => toggleZoneExpansion(zone.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-5 w-5" />
                      ) : (
                        <ChevronRight className="h-5 w-5" />
                      )}
                    </button>
                    <div className="h-16 w-16 overflow-hidden rounded-lg">
                      <img
                        src={zone.imageUrl}
                        alt={zone.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{zone.name}</h3>
                      <p className="text-sm text-gray-500">{zone.city_name} • {zone.subZone}</p>
                      <p className="text-sm text-gray-600">{zoneScreens.length} screens</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => toggleAvailability(zone.id)}
                      className={`${
                        zone.isAvailable
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      } px-2 py-1 rounded-full text-xs font-medium inline-flex items-center`}
                    >
                      <Power className="h-3 w-3 mr-1" />
                      {zone.isAvailable ? 'Active' : 'Inactive'}
                    </button>

                    <button
                      onClick={() => handleOpenScreenModal(zone)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Add Screen"
                    >
                      <Monitor className="h-5 w-5" />
                    </button>

                    <button
                      onClick={() => handleOpenModal(zone)}
                      className="text-primary-600 hover:text-primary-900"
                      title="Edit Zone"
                    >
                      <Pencil className="h-5 w-5" />
                    </button>

                    <button
                      onClick={() => handleDelete(zone.id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete Zone"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Expanded Zone Content - Screens */}
              {isExpanded && (
                <div className="p-6 bg-gray-50">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-md font-medium text-gray-900">
                      Screens in {zone.name} ({zoneScreens.length})
                    </h4>
                    <button
                      onClick={() => handleOpenScreenModal(zone)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Screen
                    </button>
                  </div>

                  {zoneScreens.length === 0 ? (
                    <p className="text-gray-500 text-sm">No screens in this zone yet.</p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {zoneScreens.map((screen) => (
                        <div key={screen.id} className="bg-white rounded-lg border p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h5 className="font-medium text-gray-900">{screen.name}</h5>
                              <p className="text-xs text-gray-500">{screen.screen_number}</p>
                              <p className="text-sm text-gray-600 mt-1">{screen.location}</p>
                              <p className="text-xs text-gray-500">{screen.size}</p>
                            </div>

                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => toggleScreenAvailability(screen.id)}
                                className={`${
                                  screen.isavailable
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                } px-1.5 py-0.5 rounded text-xs font-medium`}
                              >
                                {screen.isavailable ? 'Active' : 'Inactive'}
                              </button>

                              <button
                                onClick={() => handleOpenScreenModal(zone, screen)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                <Pencil className="h-4 w-4" />
                              </button>

                              <button
                                onClick={() => handleDeleteScreen(screen.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Zone Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-2xl w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingZone ? 'Edit Zone' : 'Add New Zone'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Zone Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="cityId" className="block text-sm font-medium text-gray-700">
                        City
                      </label>
                      <select
                        id="cityId"
                        value={formData.cityId}
                        onChange={(e) => setFormData({ ...formData, cityId: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      >
                        <option value="">Select a city</option>
                        {cities.map((city) => (
                          <option key={city.id} value={city.id}>
                            {city.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subZone" className="block text-sm font-medium text-gray-700">
                      Sub Zone
                    </label>
                    <input
                      type="text"
                      id="subZone"
                      value={formData.subZone}
                      onChange={(e) => setFormData({ ...formData, subZone: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                      placeholder="e.g., Near Forum Mall, Metro Station Exit B"
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <textarea
                      id="description"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                      Image URL
                    </label>
                    <div className="mt-1 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        id="imageUrl"
                        value={formData.imageUrl}
                        onChange={(e) => {
                          setFormData({ ...formData, imageUrl: e.target.value });
                          setPreviewImage(e.target.value);
                        }}
                        className="flex-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    {previewImage && (
                      <div className="mt-2">
                        <img
                          src={previewImage}
                          alt="Preview"
                          className="h-32 w-full object-cover rounded-md"
                          onError={() => setPreviewImage('')}
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="pricePerYear" className="block text-sm font-medium text-gray-700">
                      Price per Year
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">₹</span>
                      </div>
                      <input
                        type="number"
                        id="pricePerYear"
                        value={formData.pricePerYear}
                        onChange={(e) => setFormData({ ...formData, pricePerYear: e.target.value === '' ? '' : Number(e.target.value) })}
                        className="pl-7 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                        min="0"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Availability
                    </label>
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isAvailable}
                          onChange={(e) => setFormData({ ...formData, isAvailable: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Zone is available for booking
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCloseModal}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {editingZone ? 'Save Changes' : 'Add Zone'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Screen Modal */}
      {isScreenModalOpen && selectedZoneForScreens && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="relative bg-white rounded-lg max-w-2xl w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={handleCloseScreenModal}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingScreen ? 'Edit Screen' : 'Add New Screen'} - {selectedZoneForScreens.name}
                </h3>
                <form onSubmit={handleScreenSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="screenName" className="block text-sm font-medium text-gray-700">
                        Screen Name
                      </label>
                      <input
                        type="text"
                        id="screenName"
                        value={screenFormData.name}
                        onChange={(e) => setScreenFormData({ ...screenFormData, name: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="screenNumber" className="block text-sm font-medium text-gray-700">
                        Screen Number
                      </label>
                      <input
                        type="text"
                        id="screenNumber"
                        value={screenFormData.screenNumber}
                        onChange={(e) => setScreenFormData({ ...screenFormData, screenNumber: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                        placeholder="e.g., SCR-001"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                      Address
                    </label>
                    <input
                      type="text"
                      id="address"
                      value={screenFormData.address}
                      onChange={(e) => setScreenFormData({ ...screenFormData, address: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                        Location
                      </label>
                      <input
                        type="text"
                        id="location"
                        value={screenFormData.location}
                        onChange={(e) => setScreenFormData({ ...screenFormData, location: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                        placeholder="e.g., Main Entrance, Food Court"
                      />
                    </div>
                    <div>
                      <label htmlFor="size" className="block text-sm font-medium text-gray-700">
                        Screen Size
                      </label>
                      <select
                        id="size"
                        value={screenFormData.size}
                        onChange={(e) => setScreenFormData({ ...screenFormData, size: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                      >
                        <option value="32&quot;">32"</option>
                        <option value="42&quot;">42"</option>
                        <option value="55&quot;">55"</option>
                        <option value="65&quot;">65"</option>
                        <option value="75&quot;">75"</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Availability
                    </label>
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={screenFormData.isAvailable}
                          onChange={(e) => setScreenFormData({ ...screenFormData, isAvailable: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Screen is available for booking
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCloseScreenModal}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      {editingScreen ? 'Save Changes' : 'Add Screen'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}