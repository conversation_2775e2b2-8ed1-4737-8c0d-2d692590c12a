import { supabase } from '../supabaseClient';

// Email configuration
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email credentials - to be set later
let emailConfig: EmailConfig | null = null;
let adminEmail: string | null = null;

// Set email configuration
export const setEmailConfig = (config: EmailConfig, admin: string) => {
  emailConfig = config;
  adminEmail = admin;
};

// Email data interface
interface EmailData {
  to: string;
  subject: string;
  html: string;
}

// Log email to database
const logEmail = async (
  to: string,
  subject: string,
  bookingId: string | null,
  status: 'success' | 'failed',
  error?: string
) => {
  try {
    const { error: dbError } = await supabase.from('email_logs').insert({
      recipient: to,
      subject,
      booking_id: bookingId,
      status,
      error_message: error,
      sent_at: new Date().toISOString(),
    });

    if (dbError) {
      console.error('Error logging email:', dbError);
    }
  } catch (err) {
    console.error('Error logging email:', err);
  }
};

// Send email function using Supabase Edge Functions or a custom API
export const sendEmail = async (emailData: EmailData, bookingId: string | null = null): Promise<boolean> => {
  if (!emailConfig || !adminEmail) {
    console.error('Email configuration not set');
    return false;
  }

  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        emailData,
        bookingId,
        emailConfig,
        adminEmail
      }
    });

    if (error) throw error;
    
    // Log successful email
    await logEmail(emailData.to, emailData.subject, bookingId, 'success');
    
    return data.success;
  } catch (error: any) {
    // Log failed email
    await logEmail(emailData.to, emailData.subject, bookingId, 'failed', error.message);
    console.error('Error sending email:', error);
    return false;
  }
};

// Email templates
export const getBookingApprovedEmailTemplate = (
  businessName: string,
  zoneName: string,
  startDate: string,
  endDate: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Approved</h2>
      <p>Dear Customer,</p>
      <p>We're pleased to inform you that your booking has been approved!</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone:</strong> ${zoneName}</p>
        <p><strong>Duration:</strong> ${startDate} to ${endDate}</p>
      </div>

      <p>If you have any questions or need further assistance, please don't hesitate to contact us.</p>

      <p>Thank you for choosing our service!</p>

      <p>Best regards,<br>3Shul Admin Team</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This is an automated email. Please do not reply to this message.</p>
      </div>
    </div>
  `;
};

export const getAdminNotificationEmailTemplate = (
  businessName: string,
  zoneName: string,
  startDate: string,
  endDate: string,
  customerEmail: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Approval Notification</h2>
      <p>A booking has been approved:</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone:</strong> ${zoneName}</p>
        <p><strong>Duration:</strong> ${startDate} to ${endDate}</p>
        <p><strong>Customer Email:</strong> ${customerEmail}</p>
      </div>

      <p>The customer has been notified of this approval.</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This is an automated email. Please do not reply to this message.</p>
      </div>
    </div>
  `;
};

