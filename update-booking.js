// A script to directly update a booking's payment status using the Supabase REST API
// Run with: node update-booking.js YOUR_BOOKING_ID YOUR_SUPABASE_ANON_KEY

const https = require('https');

// Get command line arguments
const bookingId = process.argv[2];
const supabaseKey = process.argv[3];

if (!bookingId || !supabaseKey) {
  console.error('Usage: node update-booking.js YOUR_BOOKING_ID YOUR_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Construct the URL
const url = `https://xirnspqtaoyzsqlbbhcl.supabase.co/rest/v1/bookings?id=eq.${bookingId}`;

// Set up the request options
const options = {
  method: 'PATCH',
  headers: {
    'apikey': supabaseKey,
    'Authorization': `Bearer ${supabaseKey}`,
    'Content-Type': 'application/json',
    'Prefer': 'return=minimal'
  }
};

// Create the request body
const data = JSON.stringify({
  payment_status: 'success',
  status: 'Approved'
});

console.log(`Updating booking ID: ${bookingId} to success status`);
console.log(`URL: ${url}`);

// Make the request
const req = https.request(url, options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let responseData = '';
  
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    if (res.statusCode === 204 || res.statusCode === 200) {
      console.log('Booking successfully updated to success status!');
    } else {
      console.log('Response:');
      try {
        const jsonData = JSON.parse(responseData);
        console.log(JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log(responseData);
      }
    }
  });
});

req.on('error', (error) => {
  console.error(`Error: ${error.message}`);
});

// Write the data to the request body
req.write(data);
req.end();
