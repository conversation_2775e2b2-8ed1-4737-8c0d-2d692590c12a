To update a booking's payment status to 'success', you can use the following URL:

https://xirnspqtaoyzsqlbbhcl.supabase.co/rest/v1/bookings?id=eq.YOUR_BOOKING_ID

With the following headers:
- apikey: YOUR_SUPABASE_ANON_KEY
- Authorization: Bearer YOUR_SUPABASE_ANON_KEY
- Content-Type: application/json
- Prefer: return=minimal

And the following request body:
{
  "payment_status": "success",
  "status": "Approved"
}

Using curl:
```
curl -X PATCH "https://xirnspqtaoyzsqlbbhcl.supabase.co/rest/v1/bookings?id=eq.YOUR_BOOKING_ID" \
  -H "apikey: YOUR_SUPABASE_ANON_KEY" \
  -H "Authorization: Bearer YOUR_SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -H "Prefer: return=minimal" \
  -d '{"payment_status":"success","status":"Approved"}'
```

Using Postman:
1. Set the request type to PATCH
2. Enter the URL: https://xirnspqtaoyzsqlbbhcl.supabase.co/rest/v1/bookings?id=eq.YOUR_BOOKING_ID
3. Add the headers:
   - apikey: YOUR_SUPABASE_ANON_KEY
   - Authorization: Bearer YOUR_SUPABASE_ANON_KEY
   - Content-Type: application/json
   - Prefer: return=minimal
4. Add the request body:
   {
     "payment_status": "success",
     "status": "Approved"
   }
5. Click Send
