import { useState, useRef } from 'react';
import { Upload, Play, X } from 'lucide-react';

interface VideoUploadProps {
  onVideoUpload: (file: File) => void;
  currentVideoUrl?: string | null;
  onContinue?: () => void;  // Add this prop
}

export function VideoUpload({ onVideoUpload, currentVideoUrl, onContinue }: VideoUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentVideoUrl || null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      onVideoUpload(file);
    }
  };

  const handleRemoveVideo = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    if (videoRef.current) {
      videoRef.current.src = '';
    }
  };

  return (
    <div className="space-y-6">
      {!previewUrl ? (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <div className="flex flex-col items-center">
            <Upload className="h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <label
                htmlFor="video-upload"
                className="cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500"
              >
                <span>Upload a video</span>
                <input
                  id="video-upload"
                  name="video-upload"
                  type="file"
                  className="sr-only"
                  accept="video/*"
                  onChange={handleFileChange}
                />
              </label>
              <p className="text-xs text-gray-500">MP4, MOV up to 100MB</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative">
          <div className="absolute top-2 right-2 z-10">
            <button
              onClick={handleRemoveVideo}
              className="p-1 bg-red-500 rounded-full text-white hover:bg-red-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          <video
            ref={videoRef}
            src={previewUrl}
            controls
            className="w-full rounded-lg"
            style={{ maxHeight: '400px' }}
          >
            Your browser does not support the video tag.
          </video>
          <div className="mt-4 flex justify-between items-center">
            <label
              htmlFor="video-upload"
              className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Change Video
              <input
                id="video-upload"
                name="video-upload"
                type="file"
                className="sr-only"
                accept="video/*"
                onChange={handleFileChange}
              />
            </label>
            {onContinue && (
              <button
                onClick={onContinue}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Continue
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

