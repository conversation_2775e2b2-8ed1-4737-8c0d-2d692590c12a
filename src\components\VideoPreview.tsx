import { useRef, useEffect, useState } from 'react';

interface VideoPreviewProps {
  videoUrl: string;
  className?: string;
}

export function VideoPreview({ videoUrl, className = "" }: VideoPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);

  // Properly encode the URL and handle Supabase storage URLs
  const getEncodedUrl = (url: string) => {
    try {
      // Check if it's a Supabase storage URL
      if (url.includes('storage.googleapis.com') || url.includes('supabase')) {
        // Remove any query parameters that might cause CORS issues
        const urlWithoutParams = url.split('?')[0];
        return urlWithoutParams;
      }

      // For other URLs, apply standard encoding
      let decodedUrl = url;
      while (decodedUrl !== decodeURIComponent(decodedUrl)) {
        decodedUrl = decodeURIComponent(decodedUrl);
      }

      const urlObj = new URL(decodedUrl);
      const pathParts = urlObj.pathname.split('/');
      const filename = pathParts[pathParts.length - 1];
      
      const encodedFilename = filename
        .replace(/[^a-zA-Z0-9-._~\s]/g, (char) => encodeURIComponent(char))
        .replace(/\s/g, '%20');
      
      pathParts[pathParts.length - 1] = encodedFilename;
      urlObj.pathname = pathParts.join('/');
      
      return urlObj.toString();
    } catch (e) {
      console.error('URL encoding error:', e);
      return url; // Return original URL if encoding fails
    }
  };

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.load();
      setError(null);
      setVideoLoaded(false);
    }
  }, [videoUrl]);

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const target = e.target as HTMLVideoElement;
    let errorMessage = 'Failed to load video';
    
    if (target.error?.message?.includes('CORS')) {
      errorMessage = 'Cross-origin access denied. Please check CORS configuration.';
    } else if (target.error) {
      switch (target.error.code) {
        case 1:
          errorMessage = 'Video loading aborted';
          break;
        case 2:
          errorMessage = 'Network error while loading video';
          break;
        case 3:
          errorMessage = 'Error decoding video';
          break;
        case 4:
          errorMessage = 'Video format not supported';
          break;
      }
    }

    // Validate URL accessibility
    fetch(encodedVideoUrl, { method: 'HEAD' })
      .then(response => {
        if (!response.ok) {
          setError(`Video URL is not accessible (Status: ${response.status})`);
        }
        console.log('Video URL response:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        });
      })
      .catch(fetchError => {
        setError('Video URL is not accessible');
        console.error('Video URL fetch error:', fetchError);
      });

    console.error('Video error details:', {
      errorCode: target.error?.code,
      errorMessage: target.error?.message,
      originalUrl: videoUrl,
      encodedUrl: encodedVideoUrl,
      networkState: target.networkState,
      readyState: target.readyState
    });

    setError(errorMessage);
  };

  const handleLoadedData = () => {
    setVideoLoaded(true);
    setError(null);
  };

  const encodedVideoUrl = getEncodedUrl(videoUrl);

  return (
    <div className={`relative ${className}`}>
      {error && (
        <div className="text-red-500 text-sm mb-2 p-2 bg-red-50 rounded">
          <p>{error}</p>
          <p className="text-xs mt-1 break-all">
            Original URL: {videoUrl}
          </p>
          <p className="text-xs mt-1 break-all">
            Encoded URL: {encodedVideoUrl}
          </p>
        </div>
      )}
      <div className={`relative ${!videoLoaded ? 'bg-gray-100' : ''}`}>
        {!videoLoaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          </div>
        )}
        <video
          ref={videoRef}
          controls
          preload="metadata"
          crossOrigin="anonymous"
          className={`w-full rounded-lg ${!videoLoaded && !error ? 'opacity-0' : 'opacity-100'}`}
          style={{ maxHeight: '400px' }}
          onError={handleError}
          onLoadedData={handleLoadedData}
          onLoadStart={() => console.log('Video load started', { url: encodedVideoUrl })}
          onProgress={() => console.log('Video loading in progress')}
        >
          <source 
            src={encodedVideoUrl} 
            type="video/mp4"
            onError={(e) => {
              console.error('Source error:', {
                event: e,
                url: encodedVideoUrl,
                decodedUrl: decodeURIComponent(encodedVideoUrl)
              });
            }} 
          />
          Your browser does not support the video tag.
        </video>
      </div>
      <div className="mt-1 text-xs text-gray-500 break-all">
        {!error && videoLoaded && `Video loaded successfully`}
      </div>
    </div>
  );
}


