// A simple script to update a booking's payment status to 'success'
// Run this with Node.js: node test-update-booking.js YOUR_BOOKING_ID

const { createClient } = require('@supabase/supabase-js');

// Replace these with your actual Supabase URL and anon key
const SUPABASE_URL = 'https://xirnspqtaoyzsqlbbhcl.supabase.co';
const SUPABASE_ANON_KEY = 'YOUR_SUPABASE_ANON_KEY'; // Replace with your actual anon key

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function updateBookingStatus(bookingId) {
  console.log(`Updating booking ${bookingId} to success status...`);
  
  try {
    const { data, error } = await supabase
      .from('bookings')
      .update({
        payment_status: 'success',
        status: 'Approved'
      })
      .eq('id', bookingId);
    
    if (error) {
      console.error('Error updating booking:', error.message);
      return;
    }
    
    console.log(`Booking ${bookingId} successfully updated to success status!`);
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

// Get booking ID from command line argument
const bookingId = process.argv[2];

if (!bookingId) {
  console.error('Please provide a booking ID as a command line argument');
  console.error('Usage: node test-update-booking.js YOUR_BOOKING_ID');
  process.exit(1);
}

updateBookingStatus(bookingId);
