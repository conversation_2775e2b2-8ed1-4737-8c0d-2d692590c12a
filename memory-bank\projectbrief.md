# Project Brief
Trishul is a zone booking and management platform designed to streamline the process of reserving and managing commercial spaces listed on the3shul.com. The application allows businesses to easily book zones for specific durations without requiring account logins, ensuring a quick and user-friendly experience. On the backend, administrators can efficiently manage zone availability, booking requests, and time slots, all from a centralized dashboard. With integrated Razorpay payments and automated email confirmations, Trishul offers a seamless and professional booking solution tailored for commercial zone listings.
## Overview
A commercial space booking platform for digital advertising displays. Allows businesses to:
- Browse available advertising zones
- Select booking dates
- Upload promotional videos
- Complete bookings and payments

## Key Features
- Multi-step booking form
- Authentication system
- Dashboard for managing bookings
- Video upload capability
- Payment processing

## Technologies
- React (TypeScript)
- Vite
- Tailwind CSS
- React Router
- Headless UI
- Date-fns

## Database schema

-- USERS TABLE
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ZONES TABLE
CREATE TABLE zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  city VARCHAR(100) NOT NULL,
  sub_zone VARCHAR(100) NOT NULL,
  pincode VARCHAR(20),
  description TEXT,
  price_year DECIMAL(10, 2) NOT NULL,
  image_url TEXT,
  isavailable BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BOOKINGS TABLE
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  listing_type VARCHAR(100),
  business_name VARCHAR(255),
  description TEXT,
  phone_number VARCHAR(20),
  video_url TEXT,
  status VARCHAR(20) CHECK (status IN ('Pending', 'Approved', 'Fulfilled', 'Cancelled')) DEFAULT 'Pending',
  payment_id VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- BOOKING_ZONES junction table
CREATE TABLE booking_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  zone_id UUID REFERENCES zones(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(booking_id, zone_id)
);

CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  razorpay_payment_id VARCHAR(100) NOT NULL,
  razorpay_order_id VARCHAR(100),
  amount DECIMAL(10, 2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'INR',
  method VARCHAR(50),
  status VARCHAR(50),
  email VARCHAR(255),
  contact VARCHAR(20),
  fee DECIMAL(10, 2),
  tax DECIMAL(10, 2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
